# Módulo Text Hideable para EspoCRM

## Descripción

Este módulo agrega un nuevo tipo de campo llamado "Text Hideable" que combina las funcionalidades de un campo de texto con la capacidad de ocultar su contenido como si fuera un campo de contraseña.

## Características principales

✅ **Texto multilínea**: Soporta texto de múltiples líneas como un campo de texto normal  
✅ **Visibilidad toggleable**: Permite mostrar/ocultar el contenido con un botón  
✅ **Configuración flexible**: Múltiples parámetros de configuración  
✅ **Responsive**: Se adapta a diferentes tamaños de pantalla  
✅ **Internacionalización**: Soporta inglés y español  
✅ **Integración completa**: Funciona en todos los modos de vista (detail, edit, list, search)

## Estructura de archivos

```
custom/
├── Espo/Custom/Resources/
│   ├── metadata/fields/
│   │   └── textHideable.json                    # Definición del campo
│   ├── i18n/
│   │   ├── en_US/
│   │   │   ├── Admin.json                       # Traducciones inglés (admin)
│   │   │   └── Global.json                      # Traducciones inglés (global)
│   │   └── es_ES/
│   │       ├── Admin.json                       # Traducciones español (admin)
│   │       └── Global.json                      # Traducciones español (global)
│   └── docs/
│       └── text-hideable-field.md               # Documentación detallada
├── scripts/
│   └── install-text-hideable.php                # Script de instalación
└── README-text-hideable.md                      # Este archivo

client/custom/
├── src/views/fields/
│   └── text-hideable.js                         # Vista JavaScript principal
├── res/templates/fields/text-hideable/
│   ├── detail.tpl                               # Plantilla vista detalle
│   ├── edit.tpl                                 # Plantilla edición
│   ├── list.tpl                                 # Plantilla lista
│   └── search.tpl                               # Plantilla búsqueda
└── css/
    └── text-hideable.css                        # Estilos CSS
```

## Instalación

1. **Copiar archivos**: Todos los archivos ya están en su ubicación correcta en el directorio `custom/`

2. **Ejecutar script de instalación** (opcional):
   ```bash
   php custom/scripts/install-text-hideable.php
   ```

3. **Limpiar caché manualmente**:
   - Ve a Administration > Clear Cache
   - O ejecuta: `php clear_cache.php`

4. **Reconstruir**:
   - Ve a Administration > Rebuild
   - O ejecuta: `php rebuild.php`

## Uso

### Agregar campo via Entity Manager

1. Ve a **Administration > Entity Manager**
2. Selecciona la entidad donde quieres agregar el campo
3. Haz clic en **"Fields"** y luego **"Add Field"**
4. Selecciona **"Text (Hideable)"** como tipo de campo
5. Configura los parámetros:
   - **Hide by Default**: Si el texto debe estar oculto por defecto
   - **Show Toggle Button**: Si mostrar el botón para alternar visibilidad
   - **Max Length**: Longitud máxima del texto
   - **Rows**: Número de filas del textarea
6. Guarda y reconstruye

### Parámetros de configuración

| Parámetro | Tipo | Defecto | Descripción |
|-----------|------|---------|-------------|
| `hideByDefault` | Boolean | `true` | Ocultar contenido por defecto |
| `showToggleButton` | Boolean | `true` | Mostrar botón de toggle |
| `maxLength` | Integer | - | Longitud máxima del texto |
| `rows` | Integer | `4` | Número de filas del textarea |
| `required` | Boolean | `false` | Campo obligatorio |

### Ejemplo de definición manual

```json
{
    "fields": {
        "secretNotes": {
            "type": "textHideable",
            "hideByDefault": true,
            "showToggleButton": true,
            "maxLength": 1000,
            "rows": 6,
            "required": false
        }
    }
}
```

## Comportamiento por modo

### 🔍 Modo Detail (Vista de detalle)
- Muestra contenido enmascarado por defecto si `hideByDefault` es true
- Botón para alternar visibilidad si `showToggleButton` es true
- Contenido enmascarado muestra primeros caracteres + asteriscos

### ✏️ Modo Edit (Edición)
- Textarea que puede ser enmascarado
- Botón toggle permite mostrar/ocultar mientras se edita
- Al enfocar, siempre muestra contenido real para facilitar edición

### 📋 Modo List (Lista)
- Muestra contenido enmascarado si está configurado para estar oculto
- Formato compacto apropiado para vistas de lista

### 🔎 Modo Search (Búsqueda)
- Funciona como campo de texto normal para búsquedas
- Soporta todos los tipos de búsqueda de texto estándar

## Casos de uso

- 📝 Notas confidenciales
- 🔑 Información de API keys
- 💬 Comentarios internos sensibles
- 🔒 Datos que requieren privacidad ocasional
- 👤 Información personal sensible

## Compatibilidad

- ✅ EspoCRM 7.x y 8.x
- ✅ Navegadores modernos (Chrome, Firefox, Safari, Edge)
- ✅ Dispositivos móviles y tablets
- ✅ Temas estándar de EspoCRM

## Soporte

Para documentación detallada, consulta: `custom/Espo/Custom/Resources/docs/text-hideable-field.md`

## Licencia

Este módulo sigue la misma licencia que EspoCRM (GNU Affero General Public License v3.0).
