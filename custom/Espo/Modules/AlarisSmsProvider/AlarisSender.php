<?php

namespace Espo\Modules\AlarisSmsProvider;

use Espo\Core\Sms\Sender;
use Espo\Core\Sms\Sms;
use Espo\ORM\EntityManager;
use Espo\Entities\Integration;
use Espo\Entities\Sms as SmsEntity;
use Espo\Core\Exceptions\Error;
use Espo\Core\Utils\Log;

class AlarisSender implements Sender
{
    private string $apiUrl;
    private string $username;
    private string $password;
    private int $rate;

    public function __construct(
        private EntityManager $entityManager,
        private Log $log,
    ) {
        $integration = $this->getIntegrationEntity();
        $this->apiUrl = $integration->get('alarisSmsUrl');
        $this->username = $integration->get('alarisSmsUsername');
        $this->password = $integration->get('alarisSmsPassword');
        $this->rate = $integration->get('alarisSmsRate');
    }

    public function send(Sms $sms): void
    {
        $toNumberList = $sms->getToNumberList();

        if (!count($toNumberList)) {
            throw new Error("No recipient phone number.");
        }

        foreach ($toNumberList as $number) {
            $result = $this->sendToNumber($sms, $this->formatNumber($number));
            $this->processSendResult($result, $sms);
            usleep(60000 / $this->rate);
        }
    }

    private function processSendResult(array|bool $result, Sms $sms): void
    {
        if (
            $result
            && ($sms instanceof SmsEntity)
            && in_array('remoteId', $sms->getAttributeList())
        ) {
            $query = $this
                ->entityManager
                ->getQueryBuilder()
                ->update()
                ->in(SmsEntity::ENTITY_TYPE)
                ->set(['remoteId' => $result['message_id']])
                ->where(['id' => $sms->getId()])
                ->build();

            $this->entityManager->getQueryExecutor()->execute($query);
        }
    }

    private function sendToNumber(SmsEntity $sms, string $toNumber): array|bool
    {
        $payload = [
            'command' => 'submit',
            'dnis' => "+56" . $toNumber,
            'username' => $this->username,
            'password' => $this->password,
            'serviceType' => '',
            'message' => $sms->getBody()
        ];

        return $this->request($payload);
    }

    private function request(array $payload): array|bool
    {
        $ch = curl_init();
        $postFields = json_encode($payload);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $postFields);
        curl_setopt($ch, CURLOPT_HTTPHEADER, ['Content-Type: application/json']);
        curl_setopt($ch, \CURLOPT_URL, $this->apiUrl);
        curl_setopt($ch, CURLOPT_POST, 1);
        curl_setopt($ch, \CURLOPT_RETURNTRANSFER, true);

        $response = curl_exec($ch);
        $code = curl_getinfo($ch, \CURLINFO_HTTP_CODE);
        $error = curl_errno($ch);
        curl_close($ch);

        $this->log->error(sprintf("url: %s | body: %s | response: %s | code: %s", $this->apiUrl, $postFields, $response, $code));
        
        if (intval($code) > 299) {
            $this->log->error("Alaris SMS: ". $response);
            throw new Error("Alaris SMS: ". $response, intval($code));
            return false;
        }

        if ($error) {
            $this->log->error("Alaris SMS: ". $error);
            throw new Error("Alaris SMS: ". $error);
            return false;
        }

        return json_decode($response, true);
    }

    private static function formatNumber(string $number): string
    {
        return substr(preg_replace('/[^0-9]/', '', $number), -9);
    }

    private function getIntegrationEntity(): Integration
    {
        $entity = $this->entityManager
            ->getEntity(Integration::ENTITY_TYPE, 'AlarisSms');

        if (!$entity || !$entity->get('enabled')) {
            throw new Error("AlarisSms integration is not enabled");
        }

        return $entity;
    }
}
