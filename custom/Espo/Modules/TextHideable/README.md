# TextHideable Module for EspoCRM

## 🎯 Descripción

El módulo **TextHideable** agrega un nuevo tipo de campo llamado "Text (Hideable)" que combina las funcionalidades de un campo de texto con la capacidad de ocultar su contenido como si fuera un campo de contraseña.

## ✨ Características principales

- ✅ **Texto multilínea** con soporte completo para contenido extenso
- ✅ **Toggle de visibilidad** con botón para mostrar/ocultar contenido
- ✅ **Configuración flexible** con múltiples parámetros personalizables
- ✅ **Diseño responsive** que se adapta a todos los dispositivos
- ✅ **Internacionalización** con soporte para inglés y español
- ✅ **Integración completa** con todos los modos de vista de EspoCRM
- ✅ **Arquitectura modular** para fácil mantenimiento y portabilidad

## 🚀 Instalación rápida

1. **Limpiar caché**: `Administration > Clear Cache`
2. **Reconstruir**: `Administration > Rebuild`
3. **Usar**: `Entity Manager > Add Field > Text (Hideable)`

## 📁 Estructura del módulo

```
TextHideable/
├── Module.php                          # Clase principal
├── Resources/
│   ├── module.json                     # Configuración
│   ├── metadata/fields/                # Definición del campo
│   ├── i18n/                          # Traducciones (EN/ES)
│   ├── docs/                          # Documentación
│   └── scripts/                       # Scripts de instalación
└── client/modules/text-hideable/
    ├── src/views/fields/               # Vista JavaScript
    ├── res/templates/                  # Plantillas HTML
    └── css/                           # Estilos CSS
```

## ⚙️ Parámetros de configuración

| Parámetro | Tipo | Defecto | Descripción |
|-----------|------|---------|-------------|
| `hideByDefault` | Boolean | `true` | Ocultar contenido por defecto |
| `showToggleButton` | Boolean | `true` | Mostrar botón de toggle |
| `maxLength` | Integer | - | Longitud máxima del texto |
| `rows` | Integer | `4` | Número de filas del textarea |

## 💡 Casos de uso

- 📝 **Notas confidenciales** en registros de clientes
- 🔑 **API keys y tokens** de integración
- 💬 **Comentarios internos** sensibles
- 🔒 **Información personal** que requiere privacidad
- 📋 **Instrucciones especiales** no siempre visibles

## 🔧 Uso avanzado

### Definición manual en metadata

```json
{
    "fields": {
        "secretNotes": {
            "type": "textHideable",
            "hideByDefault": true,
            "showToggleButton": true,
            "maxLength": 1000,
            "rows": 6
        }
    }
}
```

### Script de instalación

```bash
php custom/Espo/Modules/TextHideable/Resources/scripts/install.php
```

## 🌐 Soporte de idiomas

- 🇺🇸 **English** (en_US)
- 🇪🇸 **Español** (es_ES)

## 📋 Compatibilidad

- ✅ EspoCRM 7.x y 8.x
- ✅ Navegadores modernos
- ✅ Dispositivos móviles
- ✅ Todos los temas de EspoCRM

## 📖 Documentación completa

Ver: `Resources/docs/README.md`

## 📄 Licencia

GNU Affero General Public License v3.0

---

**Desarrollado con ❤️ para la comunidad EspoCRM**
