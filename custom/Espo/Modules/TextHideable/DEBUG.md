# Debug Instructions for TextHideable Module

## Current Issue
The toggle button appears but doesn't respond to clicks.

## Debugging Steps

### 1. Check Browser Console
Open browser developer tools (F12) and check the console for:
- `TextHideable: initToggleButton called`
- `TextHideable: Found cell container: [object]`
- `TextHideable: Created toggle button: [object]`
- When clicking the button: `TextHideable: Button clicked!`
- `TextHideable: toggleVisibility called, current state: true/false`

### 2. Check Module Loading
In browser console, run:
```javascript
// Check if module is loaded
console.log(require.defined('text-hideable:views/fields/text-hideable'));

// Try to load module manually
require(['text-hideable:views/fields/text-hideable'], function(View) {
    console.log('Module loaded:', View);
});
```

### 3. Check Field Configuration
Verify the field is configured correctly:
- Field type: `textHideable`
- `hideByDefault`: true
- `showToggleButton`: true

### 4. Check DOM Structure
In browser console, inspect the field:
```javascript
// Find the field element
const fieldEl = document.querySelector('[data-name="YOUR_FIELD_NAME"]');
console.log('Field element:', fieldEl);

// Check for toggle button
const toggleBtn = fieldEl.closest('.field').querySelector('.text-hideable-toggle-link');
console.log('Toggle button:', toggleBtn);

// Check button events
if (toggleBtn) {
    console.log('Button events:', $._data(toggleBtn, 'events'));
}
```

### 5. Manual Button Test
In browser console:
```javascript
// Find and click button manually
const btn = document.querySelector('.text-hideable-toggle-link');
if (btn) {
    btn.click();
    console.log('Manual click executed');
}
```

## Expected Behavior
1. Button should appear on hover over the field
2. Button should have eye icon (fa-eye or fa-eye-slash)
3. Clicking should toggle between showing/hiding text
4. Console should show debug messages

## Common Issues
1. **Module not loading**: Check cache is cleared and rebuilt
2. **Button not appearing**: Check field is in detail mode and showToggleButton is true
3. **Button not clickable**: Check for CSS z-index issues or overlapping elements
4. **Events not binding**: Check jQuery version compatibility

## Files to Check
- `client/custom/modules/text-hideable/src/views/fields/text-hideable.js`
- `custom/Espo/Modules/TextHideable/Resources/metadata/fields/textHideable.json`
- Browser network tab for 404 errors on module files
