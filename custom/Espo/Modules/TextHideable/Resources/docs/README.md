# Módulo TextHideable para EspoCRM

## Descripción

El módulo TextHideable agrega un nuevo tipo de campo llamado "Text Hideable" que combina las funcionalidades de un campo de texto con la capacidad de ocultar su contenido como si fuera un campo de contraseña.

## Características principales

✅ **Texto multilínea**: Soporta texto de múltiples líneas como un campo de texto normal  
✅ **Visibilidad toggleable**: Permite mostrar/ocultar el contenido con un botón  
✅ **Configuración flexible**: Múltiples parámetros de configuración  
✅ **Responsive**: Se adapta a diferentes tamaños de pantalla  
✅ **Internacionalización**: Soporta inglés y español  
✅ **Integración completa**: Funciona en todos los modos de vista (detail, edit, list, search)  
✅ **Arquitectura modular**: Implementado como módulo independiente para fácil mantenimiento

## Estructura del módulo

```
custom/Espo/Modules/TextHideable/
├── Module.php                                   # Clase principal del módulo
├── Resources/
│   ├── module.json                              # Configuración del módulo
│   ├── metadata/fields/
│   │   └── textHideable.json                    # Definición del campo
│   ├── i18n/
│   │   ├── en_US/
│   │   │   ├── Admin.json                       # Traducciones inglés (admin)
│   │   │   └── Global.json                      # Traducciones inglés (global)
│   │   └── es_ES/
│   │       ├── Admin.json                       # Traducciones español (admin)
│   │       └── Global.json                      # Traducciones español (global)
│   └── docs/
│       └── README.md                            # Esta documentación

client/custom/modules/text-hideable/
├── src/views/fields/
│   └── text-hideable.js                         # Vista JavaScript principal
├── res/templates/fields/text-hideable/
│   ├── detail.tpl                               # Plantilla vista detalle
│   ├── edit.tpl                                 # Plantilla edición
│   ├── list.tpl                                 # Plantilla lista
│   └── search.tpl                               # Plantilla búsqueda
└── css/
    └── text-hideable.css                        # Estilos CSS
```

## Instalación

1. **Verificar archivos**: Asegúrate de que todos los archivos del módulo estén en su ubicación correcta

2. **Limpiar caché**:
   - Ve a Administration > Clear Cache
   - O ejecuta: `php clear_cache.php`

3. **Reconstruir**:
   - Ve a Administration > Rebuild
   - O ejecuta: `php rebuild.php`

## Uso

### Agregar campo via Entity Manager

1. Ve a **Administration > Entity Manager**
2. Selecciona la entidad donde quieres agregar el campo
3. Haz clic en **"Fields"** y luego **"Add Field"**
4. Selecciona **"Text (Hideable)"** como tipo de campo
5. Configura los parámetros:
   - **Hide by Default**: Si el texto debe estar oculto por defecto
   - **Show Toggle Button**: Si mostrar el botón para alternar visibilidad
   - **Max Length**: Longitud máxima del texto
   - **Rows**: Número de filas del textarea
6. Guarda y reconstruye

### Parámetros de configuración

| Parámetro | Tipo | Defecto | Descripción |
|-----------|------|---------|-------------|
| `hideByDefault` | Boolean | `true` | Ocultar contenido por defecto |
| `showToggleButton` | Boolean | `true` | Mostrar botón de toggle |
| `maxLength` | Integer | - | Longitud máxima del texto |
| `rows` | Integer | `4` | Número de filas del textarea |
| `required` | Boolean | `false` | Campo obligatorio |
| `cutHeight` | Integer | `200` | Altura de corte para "ver más" |
| `seeMoreDisabled` | Boolean | `false` | Deshabilitar "ver más" |
| `displayRawText` | Boolean | `false` | Mostrar texto sin formato |
| `audited` | Boolean | `false` | Auditar cambios |

### Ejemplo de definición manual

```json
{
    "fields": {
        "secretNotes": {
            "type": "textHideable",
            "hideByDefault": true,
            "showToggleButton": true,
            "maxLength": 1000,
            "rows": 6,
            "required": false
        }
    }
}
```

## Comportamiento por modo

### 🔍 Modo Detail (Vista de detalle)
- Muestra contenido enmascarado por defecto si `hideByDefault` es true
- Botón para alternar visibilidad si `showToggleButton` es true
- Contenido enmascarado muestra primeros caracteres + asteriscos
- Soporta funcionalidad "ver más" para textos largos

### ✏️ Modo Edit (Edición)
- Textarea que puede ser enmascarado visualmente
- Botón toggle permite mostrar/ocultar mientras se edita
- Al enfocar, siempre muestra contenido real para facilitar edición
- Auto-height y resize configurables

### 📋 Modo List (Lista)
- Muestra contenido enmascarado si está configurado para estar oculto
- Formato compacto apropiado para vistas de lista

### 🔎 Modo Search (Búsqueda)
- Funciona como campo de texto normal para búsquedas
- Soporta todos los tipos de búsqueda de texto estándar

## Casos de uso

- 📝 Notas confidenciales en registros de clientes
- 🔑 Información de API keys o tokens
- 💬 Comentarios internos sensibles
- 🔒 Datos que requieren privacidad ocasional
- 👤 Información personal sensible
- 📋 Instrucciones especiales que no siempre deben ser visibles

## Ventajas de la arquitectura modular

- **Separación de responsabilidades**: El código está organizado en un módulo independiente
- **Fácil mantenimiento**: Actualizaciones y cambios se realizan en un solo lugar
- **Reutilización**: El módulo puede ser fácilmente portado a otras instalaciones
- **Versionado**: Control de versiones independiente del core de EspoCRM
- **Desinstalación limpia**: Se puede remover completamente eliminando la carpeta del módulo

## Compatibilidad

- ✅ EspoCRM 7.x y 8.x
- ✅ Navegadores modernos (Chrome, Firefox, Safari, Edge)
- ✅ Dispositivos móviles y tablets
- ✅ Temas estándar de EspoCRM
- ✅ Compatible con otros módulos

## Desarrollo y personalización

El módulo está diseñado para ser fácilmente extensible:

- **Nuevos idiomas**: Agregar archivos en `Resources/i18n/{locale}/`
- **Estilos personalizados**: Modificar `css/text-hideable.css`
- **Funcionalidad adicional**: Extender la clase JavaScript
- **Validaciones**: Agregar validadores en el backend

## Licencia

Este módulo sigue la misma licencia que EspoCRM (GNU Affero General Public License v3.0)
