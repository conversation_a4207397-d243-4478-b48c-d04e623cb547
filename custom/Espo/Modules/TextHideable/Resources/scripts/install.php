<?php
/**
 * Installation script for TextHideable Module
 * Run this script after installing the module to clear cache and verify installation
 */

// Include EspoCRM bootstrap
require_once dirname(__DIR__, 5) . '/bootstrap.php';

$app = new \Espo\Core\Application();
$container = $app->getContainer();

echo "Installing TextHideable Module...\n";

try {
    // Clear cache
    echo "Clearing cache...\n";
    $container->get('dataManager')->clearCache();
    
    // Rebuild
    echo "Rebuilding...\n";
    $container->get('dataManager')->rebuild();
    
    // Verify module is loaded
    $moduleUtil = $container->get('moduleUtil');
    $modules = $moduleUtil->getList();
    
    if (in_array('TextHideable', $modules)) {
        echo "✓ TextHideable module loaded successfully!\n";
    } else {
        echo "⚠ Warning: TextHideable module not detected in module list\n";
    }
    
    echo "\n✓ TextHideable module installed successfully!\n";
    echo "\nNext steps:\n";
    echo "1. Go to Administration > Entity Manager\n";
    echo "2. Select an entity and add a new field\n";
    echo "3. Choose 'Text (Hideable)' as the field type\n";
    echo "4. Configure the field parameters as needed\n";
    echo "5. Save and rebuild\n";
    echo "\nModule features:\n";
    echo "- Toggle visibility of text content\n";
    echo "- Configurable hiding behavior\n";
    echo "- Responsive design\n";
    echo "- Multi-language support (EN/ES)\n";
    echo "- Full integration with all view modes\n";
    
} catch (Exception $e) {
    echo "✗ Error during installation: " . $e->getMessage() . "\n";
    exit(1);
}
