<?

namespace Espo\Modules\Bsale\Services;

use Espo\Core\ORM\EntityManager;
use Espo\Core\Utils\Log;
use Espo\Modules\Bsale\Core\Client;


class Bsale {
    public readonly Client $client;
    
    public function __construct(
        private EntityManager $entityManager,
        private Log $log
    )
    {
         /** @var \Espo\Entities\Integration */
         $integration = $this->entityManager->getEntity('Integration', 'Bsale');
         $config = $integration->get('data');
         $this->client = new Client($config->accessToken ?? '', $log);
    }

    public function postDocument(array $params)
    {
        [$response] = $this->client->request("/v1/documents.json", [
            "documentTypeId" => $params['documentTypeId'],
            "officeId" => 1,
            "emissionDate" => $params['epochDate'],
            "details" => [
                [
                    "quantity" => 1,
                    "comment" => $params['comment'],
                    "netUnitValue" => $params['amount']
                ]
            ]
        ]);

        return $response;
    }
}
