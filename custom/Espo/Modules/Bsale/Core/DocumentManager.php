<?

namespace Espo\Modules\Bsale\Core;

use Espo\Core\ORM\Entity;
use Espo\Core\ORM\EntityManager;
use Espo\Modules\Bsale\Core\Utils;
use Espo\Modules\Bsale\Services\Bsale\Bsale;

class DocumentManager {

    private string $documentTypeId;

    public function __construct(
        private EntityManager $entityManager,
        private Bsale $bsale
    ) {
        /** @var \Espo\Entities\Integration */
        $integration = $this->entityManager->getEntity('Integration', 'Bsale');
        $config = $integration->get('data');
        $this->documentTypeId = $config->documentTypeId;
    }

    public function create(Entity $payment)
    {
        $pdf = $this->requestPdf($payment->get('name'), $payment->get('amount'));
        $attachment = $this->createAttachment($payment, $payment->get('name') .".pdf", $pdf);
    }

    public function requestPdf(string $name, string $amount)
    {
        $params = [
            "documentTypeId" => $this->documentTypeId,
            "epochDate" => Utils::getTodayEpoch(),
            "comment" => $name,
            "amount" => $amount
        ];

        $response = $this->bsale->postDocument($params);
        $pdfUrl = $response['urlPdfOriginal'];

        return $this->bsale->client->downloadFile($pdfUrl);
    }

    public function createAttachment(Entity $entity, string $name, string $data)
    {
        $attachment = $this->entityManager->getNewEntity('Attachment');
        $attachment->set('contents', $data);
        $attachment->set('name', $name);
        $attachment->set('type', 'application/pdf');
        $attachment->set('role', "Attachment");
        $attachment->set('parentType', $entity->getEntityType());
        $attachment->set('parentId', $entity->getId());
        $this->entityManager->saveEntity($attachment);

        return $attachment;
    }
}