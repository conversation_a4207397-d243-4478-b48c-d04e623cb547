<?

namespace Espo\Modules\Bsale\Core;

use Espo\Core\Utils\Log;

class Client {
    protected const API_URL = 'https://api.bsale.cl';

    public function __construct(protected string $accessToken, protected Log $log) {}

    public function request(string $endpoint, array $data = [], $method = 'POST') : array
    {
        $url = self::API_URL . $endpoint;
        $jsonData = empty($data) ? '' : json_encode($data);
        $ch = curl_init();
        curl_setopt_array($ch, [
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => $method,
            CURLOPT_HTTPHEADER => [
                "accept: application/json",
                "content-type: application/json",
                "access_token: ". $this->accessToken
            ],
            CURLOPT_POSTFIELDS => $jsonData
        ]);

        $this->log->info($method ." ".$url);
        $this->log->info($jsonData);
        $response = curl_exec($ch);
        $httpcode = curl_getinfo($ch, CURLINFO_HTTP_CODE);

        $err = curl_error($ch);

        curl_close($ch);

        return $this->validateResponse($response, $err, $httpcode);
    }

    public function downloadFile(string $url)
    {
        return file_get_contents($url);
    }

    /**
     * @param string $response
     */
    protected function validateResponse($response, $err, $httpCode)
    {
        if (intval($httpCode) > 299) {
            $this->log->info($response);
        }

        if ($err) {
            $this->log->info($err);
        }

        return [ json_decode($response, true), intval($httpCode), $err ];
    }
}