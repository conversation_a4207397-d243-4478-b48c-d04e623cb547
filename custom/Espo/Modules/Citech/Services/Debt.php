<?php

namespace Espo\Modules\Citech\Services;

use Espo\Core\ORM\Repository\Option\SaveOption;
use Espo\ORM\Query\Part\Condition as Cond;

class Debt extends \Espo\Core\Templates\Services\Base
{
    public function findByVdCampaignIdAndDebtorRut(string $vdCampaignId, string $debtorRut)
    {
        $creditor = $this->getCreditorByVdCampaignId($vdCampaignId);

        if (!$creditor) {
            return [];
        }

        $creditorRut = $creditor->get('rut');

        $debts = $this->entityManager->getRDBRepository('Debt')->where(Cond::and(
            Cond::equal(Cond::column('creditorRut'), $creditorRut),
            Cond::equal(Cond::column('debtorRut'), $debtorRut)
        ))->find()->getValueMapList();

        return $debts;
    }

    public function getSiteUrl()
    {
        return $this->getConfig()->get('siteUrl');
    }

    public function updateDebtByDebtorRutAndVdCampaignIdAndPhoneNumber(string $debtorRut, string $vdCampaignId, string $phoneNumber)
    {
        $creditor = $this->getCreditorByVdCampaignId($vdCampaignId);
        $creditorRut = $creditor->get('rut');

        $updateQuery = $this->entityManager
        ->getQueryBuilder()
        ->update()
        ->in('Debt')
        ->set(['phoneNumber' => $phoneNumber])
        ->where([
            'debtorRut' => $debtorRut,
            'creditorRut' => $creditorRut,
        ])
        ->build();

        $this->entityManager->getQueryExecutor()->execute($updateQuery);
    }

    private function getCreditorByVdCampaignId(string $vdCampaignId)
    {
        return $this->entityManager->getRDBRepository('Creditor')->where(
            Cond::equal(Cond::column('vdCampaignId'), $vdCampaignId)
        )->findOne();
    }
}
