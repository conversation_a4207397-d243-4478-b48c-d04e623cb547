<?php

namespace Espo\Modules\Citech\Repositories;

use Espo\ORM\Entity as IEntity;
use Espo\Core\ORM\Entity;
use Espo\ORM\Query\Part\Condition as Cond;

class Interaction extends \Espo\Core\Templates\Repositories\Base
{
    /**
     * @param Entity $entity 
     * @param array<string,mixed> $options 
     * @return void 
     */
    protected function beforeSave(IEntity $interaction, array $options = []): void
    {
        parent::beforeSave($interaction, $options);

        if (
            $interaction->isAttributeChanged('status') ||
            $interaction->isAttributeChanged('subStatus')
        ) {
            $name = sprintf("%s > %s", $interaction->get('status'), $interaction->get('subStatus'));
            $interaction->set('name', $name);
        }

        if (
            !$interaction->has('debtorId') ||
            !$interaction->has('creditorId') ||
            $interaction->isAttributeChanged('debtId')
        ) {
            $debt = $this->entityManager->getEntityById('Debt', $interaction->get('debtId'));
            $interaction->set([
                'debtorId'=> $debt->get('debtorId'),
                'creditorId' => $debt->get('creditorId')
            ]);
        }
    }
}
