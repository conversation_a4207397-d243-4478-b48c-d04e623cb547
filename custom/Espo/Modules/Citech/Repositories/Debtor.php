<?php

namespace Espo\Modules\Citech\Repositories;

use Espo\ORM\Entity as IEntity;
use Espo\Core\ORM\Entity;
use Espo\ORM\Query\Part\Condition as Cond;

class Debtor extends \Espo\Core\Templates\Repositories\Base
{
    /**
     * @param Entity $entity 
     * @param array<string,mixed> $options 
     * @return void 
     */
    protected function beforeSave(IEntity $debtor, array $options = []): void
    {
        parent::beforeSave($debtor, $options);

        if ($debtor->has('creditorId')) return;
        $creditorRut = $debtor->get('creditorRut');

        $creditor = $this->entityManager->getRDBRepository('Creditor')->where(
            Cond::equal(Cond::column('rut'), $creditorRut)
        )->findOne();

        if (!$creditor) return;

        $debtor->set('creditorId', $creditor->getId());
    }
}
