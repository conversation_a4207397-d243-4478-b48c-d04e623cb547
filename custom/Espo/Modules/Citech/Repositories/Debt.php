<?php

namespace Espo\Modules\Citech\Repositories;

use Espo\ORM\Entity as IEntity;
use Espo\Core\ORM\Entity;
use Espo\Core\ORM\Repository\Option\SaveOption;
use Espo\ORM\Query\Part\Condition as Cond;

class Debt extends \Espo\Core\Templates\Repositories\Base
{
    /**
     * @param Entity $entity 
     * @param array<string,mixed> $options 
     * @return void 
     */
    protected function beforeSave(IEntity $debt, array $options = []): void
    {
        if ($debt->isNew()) {
            $debt->set([
                'createdById' => 'system',
                'assignedUserId' => 'system',
                'name' => $debt->get('documentId')
            ]);

            $options = array_merge($options, [
                SaveOption::CREATED_BY_ID => 'system'
            ]);
        }

        parent::beforeSave($debt, $options);

        if (!$debt->has('debtorId') && !$debt->has('creditorId')) {
            $debtorRut = $debt->get('debtorRut');
            $creditorRut = $debt->get('creditorRut');
    
            $debtor = $this->entityManager->getRDBRepository('Debtor')->where(
                Cond::and(
                    Cond::equal(Cond::column('rut'), $debtorRut),
                    Cond::equal(Cond::column('creditorRut'), $creditorRut)
                )
            )->findOne();
           
            if ($debtor) {
                $debt->set([
                    'debtorId' => $debtor->getId(),
                    'creditorId' => $debtor->get('creditorId')
                ]);
            };
        }

        if (!$debt->isNew() && 
            $debt->has('status') && $debt->isAttributeChanged('status') &&
            $debt->has('subStatus') && $debt->isAttributeChanged('subStatus')
        ) {
            $this->entityManager->createEntity('Interaction', [
                'id' => $this->recordIdGenerator->generate(),
                'status' => $debt->get('status'),
                'subStatus' => $debt->get('subStatus'),
                'description' => $debt->get('comment'),
                'creditorId' => $debt->get('creditorId'),
                'debtorId' => $debt->get('debtorId'),
                'debtId' => $debt->getId(),
                'commitmentDate' => $debt->get('commitmentDate'),
                'commitmentAmount' => $debt->get('commitmentAmount'),
                'phoneNumber' => $debt->get('phoneNumber')
            ]);

            $debt->set([
                'status' => null,
                'subStatus' => null,
                'comment' => null,
                'commitmentDate' => null,
                'commitmentAmount' => null,
                'phoneNumber' => null
            ]);
        }
    }
}
