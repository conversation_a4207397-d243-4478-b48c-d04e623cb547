<?php

namespace Espo\Modules\Citech\Controllers;

use Espo\Core\Api\Request;
use Espo\Core\Api\Response;

class Debt extends \Espo\Core\Templates\Controllers\Base
{
    public function getActionFindByCampaignIdAndDebtorRut(Request $request, Response $response)
    {
        $campaignId = $request->getQueryParam('campaignId');
        $debtorRut = $request->getQueryParam('debtorRut');
        $phoneNumber = $request->getQueryParam('phoneNumber');

        $this->getRecordService()->updateDebtByDebtorRutAndVdCampaignIdAndPhoneNumber($debtorRut, $campaignId, $phoneNumber);

        $debts = $this->getRecordService()->findByVdCampaignIdAndDebtorRut($campaignId, $debtorRut);

        $count = count($debts);
        if ($count == 0) {
            $response->setStatus(404);
            return $response;
        }

        $siteUrl = $this->getRecordService()->getSiteUrl();
        if ($count == 1) {
            $response->addHeader('Location', sprintf('%s/#Debt/view/%s', $siteUrl, $debts[0]->id));
        }

        if ($count > 1) {
            $response->addHeader('Location', sprintf('%s/#Debtor/view/%s', $siteUrl, $debts[0]->debtorId));
        }

        $response->setStatus(301);
        return $response;
    }
}
