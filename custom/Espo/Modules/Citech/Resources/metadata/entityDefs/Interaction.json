{"fields": {"name": {"type": "<PERSON><PERSON><PERSON>", "required": false, "trim": true, "pattern": "$noBadCharacters", "options": [], "readOnly": true}, "description": {"type": "text", "rows": 10}, "createdAt": {"type": "datetime", "readOnly": true}, "modifiedAt": {"type": "datetime", "readOnly": true}, "createdBy": {"type": "link", "readOnly": true, "view": "views/fields/user"}, "modifiedBy": {"type": "link", "readOnly": true, "view": "views/fields/user"}, "assignedUser": {"type": "link", "required": true, "view": "views/fields/assigned-user"}, "teams": {"type": "linkMultiple", "view": "views/fields/teams"}, "status": {"type": "enum", "style": {"Contacto Directo": null, "Contacto Indirecto": null, "Sin Contacto": null}, "isCustom": true, "options": ["Contacto Directo", "Contacto Indirecto", "<PERSON>"], "default": "Contacto Directo", "required": true}, "subStatus": {"type": "enum", "style": {"": null, "Agrega / Corrige datos": null, "Buzón de Voz": null, "Cliente cesante": null, "Cliente fallecido": null, "Completo pedido socia o gerente": null, "Compromiso de pago": null, "Compromiso de repactación": null, "Confirma mail": null, "Confirma venta": null, "Consultora corta llamada": null, "Consultora mudada": null, "Consultora no puede pagar": null, "Consultora no quiere pagar": null, "Contesta titular": null, "Corta llamada": null, "Desastre natural": null, "Deudor fallecido": null, "Devolvió productos (llamada entrante)": null, "Cliente cancela (llamada entrante)": null, "Dice que pagó el total de la cuenta": null, "Dice que realizó repactación": null, "Dirección falsa": null, "Email mensaje enviado": null, "Envío contrato domicilio": null, "Error al depositar al banco": null, "Fono no contesta": null, "Fono no existe": null, "Fuera de servicio": null, "Linea ocupada": null, "Llamado BOT atendida": null, "Medio de comunicación virtual - Email": null, "Medio de comunicación virtual - Masivo": null, "Medio de comunicación virtual - SMS": null, "Medio de comunicación virtual - WhatsApp": null, "Mensaje con familiar": null, "Mensaje con tercero": null, "Negativa de pago": null, "No contesta / mensaje en grabadora": null, "No contesta contactado": null, "No contestan": null, "No hizo pedido": null, "No puede pagar": null, "No quiere pagar": null, "No recibe recado": null, "No recibió pedido": null, "No reconoce deuda": null, "Número no existe": null, "Número ya no pertenece": null, "Nunca se inscribió": null, "Pagó a socia o gerente": null, "Pago realizado": null, "Persona corta llamado": null, "Posible anulación": null, "Promesa de pago": null, "Promesa parcial (Llamada entrante)": null, "Promesa total (Llamada entrante)": null, "Reagenda promesa parcial": null, "Reagenda promesa parcial (Llamada entrante)": null, "Reagenda promesa total": null, "Reagenda promesa total (Llamada entrante)": null, "Reclamo": null, "Recordar promesa parcial (Llamada entrante)": null, "Recordar promesa total (Llamada entrante)": null, "Retira contrato parque": null, "Se deja mensaje": null, "Se deja recado": null, "Sin compromiso": null, "SMS enviado": null, "Teléfono no corresponde al cliente": null, "Teléfono fuera de servicio": null, "Teléfono no contesta": null, "Teléfono no corresponde": null, "Teléfono tono ocupado": null, "Tercero no recibe recado": null, "Volver a llamar": null, "WhatsApp mensaje entregado": null}, "isCustom": true, "required": true, "options": ["", "Agrega / Corrige datos", "Buzón de Voz", "Cliente cesante", "Cliente fallecido", "Completo pedido socia o gerente", "Compromiso de pago", "Compromiso de repactación", "Confirma mail", "Confirma venta", "Consultora corta llamada", "Consultora mudada", "Consultora no puede pagar", "Consultora no quiere pagar", "Contesta titular", "<PERSON>rta llamada", "Desastre natural", "<PERSON><PERSON><PERSON>", "Devolvió productos (llamada entrante)", "Cliente cancela (llamada entrante)", "Dice que pagó el total de la cuenta", "Dice que realizó repactación", "Dirección falsa", "Email mensaje enviado", "Envío contrato domicilio", "Error al depositar al banco", "Fono no contesta", "Fono no existe", "Fuera de servicio", "Linea ocupada", "Llamado BOT atendida", "Medio de comunicación virtual - Email", "Medio de comunicación virtual - Masivo", "Medio de comunicación virtual - SMS", "Medio de comunicación virtual - WhatsApp", "<PERSON><PERSON><PERSON> con <PERSON>", "Mensaje con tercero", "Negativa de pago", "No contesta / mensaje en grabadora", "No contesta contactado", "No contestan", "No hizo pedido", "No puede pagar", "No quiere pagar", "No recibe recado", "No recibió pedido", "No reconoce deuda", "Número no existe", "Número ya no pertenece", "Nunca se inscribió", "Pagó a socia o gerente", "Pago realizado", "Persona corta llamado", "Posible anulación", "Promesa de pago", "Promesa parcial (Llamada entrante)", "Promesa total (Llamada entrante)", "Reagenda promesa parcial", "Reagenda promesa parcial (Llamada entrante)", "Reagenda promesa total", "Reagenda promesa total (Llamada entrante)", "Re<PERSON>lam<PERSON>", "Recordar promesa parcial (Llamada entrante)", "Recordar promesa total (Llamada entrante)", "Retira contrato parque", "Se deja mensaje", "Se deja recado", "Sin compromiso", "SMS enviado", "Teléfono no corresponde al cliente", "Teléfono fuera de servicio", "Teléfono no contesta", "Teléfono no corresponde", "Teléfono tono ocupado", "Tercero no recibe recado", "<PERSON><PERSON> a llamar", "WhatsApp mensaje entregado"]}, "debt": {"type": "link"}, "debtor": {"type": "link"}, "creditor": {"type": "link"}, "commitmentDate": {"notNull": false, "type": "date", "isCustom": true}, "commitmentAmount": {"type": "currency", "isCustom": true}, "phoneNumber": {"type": "<PERSON><PERSON><PERSON>", "maxLength": 30, "readOnly": true, "options": [], "isCustom": true}}, "links": {"createdBy": {"type": "belongsTo", "entity": "User"}, "modifiedBy": {"type": "belongsTo", "entity": "User"}, "assignedUser": {"type": "belongsTo", "entity": "User"}, "teams": {"type": "hasMany", "entity": "Team", "relationName": "EntityTeam", "layoutRelationshipsDisabled": true}, "debt": {"type": "belongsTo", "foreign": "interactions", "entity": "Debt", "audited": false, "isCustom": true, "columnAttributeMap": {"creditor": "creditor", "debtor": "debtor"}}, "debtor": {"type": "belongsTo", "foreign": "interactions", "entity": "Debtor", "audited": false, "isCustom": true, "columnAttributeMap": {"creditor": "creditor"}}, "creditor": {"type": "belongsTo", "foreign": "interactions", "entity": "Creditor", "audited": false, "isCustom": true}}, "collection": {"orderBy": "createdAt", "order": "desc", "textFilterFields": ["name", "creditor.rut", "creditor.name", "debt.documentId", "debtor.rut", "debtor.name"], "fullTextSearch": false, "countDisabled": false}, "indexes": {"name": {"columns": ["name", "deleted"]}, "assignedUser": {"columns": ["assignedUserId", "deleted"]}}, "optimisticConcurrencyControl": false}