{"fields": {"name": {"type": "<PERSON><PERSON><PERSON>", "required": true, "trim": true, "pattern": "$noBadCharacters"}, "description": {"type": "text"}, "createdAt": {"type": "datetime", "readOnly": true}, "modifiedAt": {"type": "datetime", "readOnly": true}, "createdBy": {"type": "link", "readOnly": true, "view": "views/fields/user"}, "modifiedBy": {"type": "link", "readOnly": true, "view": "views/fields/user"}, "assignedUser": {"type": "link", "required": true, "view": "views/fields/assigned-user"}, "teams": {"type": "linkMultiple", "view": "views/fields/teams"}, "rut": {"type": "<PERSON><PERSON><PERSON>", "required": true, "maxLength": 150, "pattern": null, "options": [], "isCustom": true}, "creditorRut": {"type": "<PERSON><PERSON><PERSON>", "required": true, "maxLength": 150, "options": [], "isCustom": true}, "debtType": {"type": "enum", "style": [], "isCustom": true}, "creditor": {"type": "link"}, "debts": {"type": "linkMultiple", "layoutDetailDisabled": true, "layoutMassUpdateDisabled": true, "layoutListDisabled": true, "noLoad": true, "importDisabled": true, "exportDisabled": true, "customizationDisabled": true, "isCustom": true}, "interactions": {"type": "linkMultiple", "layoutDetailDisabled": true, "layoutMassUpdateDisabled": true, "layoutListDisabled": true, "noLoad": true, "importDisabled": true, "exportDisabled": true, "customizationDisabled": true, "isCustom": true}}, "links": {"createdBy": {"type": "belongsTo", "entity": "User"}, "modifiedBy": {"type": "belongsTo", "entity": "User"}, "assignedUser": {"type": "belongsTo", "entity": "User"}, "teams": {"type": "hasMany", "entity": "Team", "relationName": "EntityTeam", "layoutRelationshipsDisabled": true}, "creditor": {"type": "belongsTo", "foreign": "debtors", "entity": "Creditor", "audited": false, "isCustom": true}, "debts": {"type": "hasMany", "foreign": "debtor", "entity": "Debt", "audited": false, "isCustom": true}, "interactions": {"type": "hasMany", "foreign": "debtor", "entity": "Interaction", "audited": false, "isCustom": true}}, "collection": {"orderBy": "createdAt", "order": "desc", "textFilterFields": ["name", "rut"], "fullTextSearch": false, "countDisabled": false}, "indexes": {"name": {"columns": ["name", "deleted"]}, "assignedUser": {"columns": ["assignedUserId", "deleted"]}, "creditorIdRut": {"unique": true, "columns": ["rut", "creditorId"]}}, "optimisticConcurrencyControl": false}