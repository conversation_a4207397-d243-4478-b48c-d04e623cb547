{"controller": "controllers/record", "boolFilterList": ["onlyMy"], "iconClass": "fas fa-file-invoice-dollar", "dynamicLogic": {"fields": {"subStatus": {"invalid": {"conditionGroup": [{"type": "isNotEmpty", "attribute": "status"}, {"type": "isEmpty", "attribute": "subStatus"}]}}, "comment": {"invalid": {"conditionGroup": [{"type": "isNotEmpty", "attribute": "status"}, {"type": "isEmpty", "attribute": "comment"}]}}, "status": {"readOnly": {"conditionGroup": [{"type": "isNotEmpty", "attribute": "subStatus"}]}}, "commitmentDate": {"required": null}, "commitmentAmount": {"required": null}}, "options": {"subStatus": [{"optionList": [""], "conditionGroup": [{"type": "isEmpty", "attribute": "status"}]}, {"optionList": ["", "Sin compromiso", "Compromiso de pago", "Compromiso de repactación", "Dice que pagó el total de la cuenta", "Dice que realizó repactación", "No quiere pagar", "No puede pagar", "No reconoce deuda", "Re<PERSON>lam<PERSON>", "Cliente cesante", "SMS enviado", "WhatsApp mensaje entregado", "Email mensaje enviado", "Llamado BOT atendida"], "conditionGroup": [{"type": "equals", "attribute": "creditorId", "data": {"field": "creditor", "values": {"creditorName": "CORONA"}}, "value": "63cf49d3a49c4f9a8"}, {"type": "equals", "attribute": "status", "value": "Contacto Directo"}]}, {"optionList": ["", "Se deja recado", "<PERSON><PERSON><PERSON>", "Teléfono no corresponde al cliente", "No recibe recado", "Persona corta llamado"], "conditionGroup": [{"type": "equals", "attribute": "creditorId", "data": {"field": "creditor", "values": {"creditorName": "CORONA"}}, "value": "63cf49d3a49c4f9a8"}, {"type": "equals", "attribute": "status", "value": "Contacto Indirecto"}]}, {"optionList": ["", "Buzón de Voz", "Teléfono no contesta", "Teléfono fuera de servicio", "Teléfono tono ocupado"], "conditionGroup": [{"type": "equals", "attribute": "creditorId", "data": {"field": "creditor", "values": {"creditorName": "CORONA"}}, "value": "63cf49d3a49c4f9a8"}, {"type": "equals", "attribute": "status", "value": "<PERSON>"}]}, {"optionList": ["", "Contesta titular", "No contesta contactado", "Promesa de pago", "Negativa de pago", "Pago realizado"], "conditionGroup": [{"type": "equals", "attribute": "creditorId", "data": {"field": "creditor", "values": {"creditorName": "PARQUES CHILE"}}, "value": "63cf12171b54d584f"}, {"type": "equals", "attribute": "status", "value": "Contacto Directo"}]}, {"optionList": ["", "<PERSON><PERSON><PERSON> con <PERSON>", "Número ya no pertenece", "Se deja mensaje"], "conditionGroup": [{"type": "equals", "attribute": "creditorId", "data": {"field": "creditor", "values": {"creditorName": "PARQUES CHILE"}}, "value": "63cf12171b54d584f"}, {"type": "equals", "attribute": "status", "value": "Contacto Indirecto"}]}, {"optionList": ["", "Linea ocupada", "No contestan", "Fuera de servicio", "Número no existe"], "conditionGroup": [{"type": "equals", "attribute": "creditorId", "data": {"field": "creditor", "values": {"creditorName": "PARQUES CHILE"}}, "value": "63cf12171b54d584f"}, {"type": "equals", "attribute": "status", "value": "<PERSON>"}]}]}}}