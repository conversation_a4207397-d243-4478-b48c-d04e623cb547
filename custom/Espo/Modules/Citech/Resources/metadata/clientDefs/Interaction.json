{"controller": "controllers/record", "boolFilterList": ["onlyMy"], "iconClass": "fas fa-headset", "dynamicLogic": {"options": {"subStatus": [{"optionList": ["", "Sin compromiso", "Compromiso de pago", "Compromiso de repactación", "Dice que pagó el total de la cuenta", "Dice que realizó repactación", "No quiere pagar", "No puede pagar", "No reconoce deuda", "Re<PERSON>lam<PERSON>", "Cliente cesante", "SMS enviado", "WhatsApp mensaje entregado", "Email mensaje enviado", "Llamado BOT atendida"], "conditionGroup": [{"type": "equals", "attribute": "creditorId", "data": {"field": "creditor", "values": {"creditorName": "CORONA"}}, "value": "63cf49d3a49c4f9a8"}, {"type": "equals", "attribute": "status", "value": "Contacto Directo"}]}, {"optionList": [""], "conditionGroup": [{"type": "isEmpty", "attribute": "creditorId", "data": {"field": "creditor"}}]}]}}, "kanbanViewMode": false, "color": null}