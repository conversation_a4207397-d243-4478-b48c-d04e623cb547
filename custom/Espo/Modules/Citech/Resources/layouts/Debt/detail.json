[{"rows": [[{"name": "documentId"}, {"name": "debtType"}, {"name": "debtAmount"}], [{"name": "issuanceDate"}, {"name": "dueDate"}, {"name": "numberOfInstallments"}], [{"name": "description"}], [{"name": "phoneNumber"}, false]], "style": "default", "label": "Overview"}, {"rows": [[{"name": "status"}, {"name": "subStatus"}], [{"name": "comment"}]], "dynamicLogicVisible": null, "style": "default", "dynamicLogicStyled": null, "tabBreak": false, "hidden": false, "customLabel": "Crear Interacción"}, {"rows": [[{"name": "commitmentDate"}, {"name": "commitmentAmount"}]], "dynamicLogicVisible": {"conditionGroup": [{"type": "in", "attribute": "subStatus", "value": ["Compromiso de pago", "Compromiso de repactación", "Promesa de pago"]}]}, "style": "default", "tabBreak": false, "hidden": false, "customLabel": ""}]