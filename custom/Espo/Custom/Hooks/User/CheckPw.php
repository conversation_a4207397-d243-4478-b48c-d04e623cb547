<?php

namespace Espo\Custom\Hooks\User;

use Espo\Core\Hook\Hook\BeforeSave;
use Espo\Core\Utils\Log;
use Espo\ORM\Entity;
use Espo\ORM\Repository\Option\SaveOptions;

class CheckPw implements BeforeSave
{

    public function __construct(
        private Log $log
    ) {}
    
    public function beforeSave(Entity $entity, SaveOptions $options): void
    {
        $this->log->error('checkpw');
        $this->log->error($entity->has('passwordConfirm') ? 1 : 0);    
        $this->log->error($entity->get('passwordConfirm') ?? '-'); 
    }
    
}