# Text Hideable Field

## Descripción

El campo "Text Hideable" es un tipo de campo personalizado que combina las funcionalidades de un campo de texto con la capacidad de ocultar su contenido como si fuera un campo de contraseña. Es útil para almacenar información sensible que ocasionalmente necesita ser visible.

## Características

- **Texto multilínea**: Soporta texto de múltiples líneas como un campo de texto normal
- **Visibilidad toggleable**: Permite mostrar/ocultar el contenido con un botón
- **Configuración flexible**: Múltiples parámetros de configuración
- **Responsive**: Se adapta a diferentes tamaños de pantalla
- **Internacionalización**: Soporta múltiples idiomas

## Parámetros de configuración

### hideByDefault
- **Tipo**: Boolean
- **Valor por defecto**: true
- **Descripción**: Cuando está habilitado, el contenido del texto estará oculto por defecto

### showToggleButton
- **Tipo**: Boolean
- **Valor por defecto**: true
- **Descripción**: Cuando está habilitado, muestra un botón para alternar la visibilidad

### maxLength
- **Tipo**: Integer
- **Descripción**: Longitud máxima del texto

### rows
- **Tipo**: Integer
- **Valor por defecto**: 4
- **Descripción**: Número de filas del textarea en modo edición

### required
- **Tipo**: Boolean
- **Valor por defecto**: false
- **Descripción**: Si el campo es obligatorio

## Uso

### En Entity Manager

1. Ve a Administration > Entity Manager
2. Selecciona la entidad donde quieres agregar el campo
3. Haz clic en "Fields" y luego "Add Field"
4. Selecciona "Text (Hideable)" como tipo de campo
5. Configura los parámetros según tus necesidades
6. Guarda y reconstruye

### Ejemplo de definición en metadata

```json
{
    "fields": {
        "secretNotes": {
            "type": "textHideable",
            "hideByDefault": true,
            "showToggleButton": true,
            "maxLength": 1000,
            "rows": 6,
            "required": false
        }
    }
}
```

## Comportamiento

### Modo Detail (Vista de detalle)
- Muestra el contenido enmascarado por defecto si `hideByDefault` es true
- Incluye un botón para alternar la visibilidad si `showToggleButton` es true
- El contenido enmascarado muestra los primeros caracteres seguidos de asteriscos

### Modo Edit (Edición)
- Muestra un textarea que puede ser enmascarado
- El botón de toggle permite mostrar/ocultar el contenido mientras se edita
- Cuando está enfocado, siempre muestra el contenido real para facilitar la edición

### Modo List (Lista)
- Muestra el contenido enmascarado si está configurado para estar oculto
- Formato compacto apropiado para vistas de lista

### Modo Search (Búsqueda)
- Funciona como un campo de texto normal para búsquedas
- Soporta todos los tipos de búsqueda de texto estándar

## Estilos CSS

El campo incluye estilos CSS personalizados que:
- Enmascaran el texto usando propiedades CSS modernas
- Proporcionan fallbacks para navegadores más antiguos
- Son responsive y se adaptan a dispositivos móviles
- Mantienen la usabilidad durante la edición

## Archivos del módulo

- `custom/Espo/Custom/Resources/metadata/fields/textHideable.json` - Definición del campo
- `client/custom/src/views/fields/text-hideable.js` - Vista JavaScript
- `client/custom/res/templates/fields/text-hideable/` - Plantillas HTML
- `client/custom/css/text-hideable.css` - Estilos CSS
- `custom/Espo/Custom/Resources/i18n/` - Traducciones

## Casos de uso

- Notas confidenciales
- Información de API keys
- Comentarios internos sensibles
- Datos que requieren privacidad ocasional
- Campos que contienen información personal sensible
