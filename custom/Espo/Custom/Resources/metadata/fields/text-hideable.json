{"params": [{"name": "required", "type": "bool", "default": false}, {"name": "default", "type": "<PERSON><PERSON><PERSON>"}, {"name": "max<PERSON><PERSON><PERSON>", "type": "int", "default": 255, "min": 1, "max": 65535}, {"name": "pattern", "type": "<PERSON><PERSON><PERSON>", "default": null, "tooltip": true, "view": "views/admin/field-manager/fields/pattern"}, {"name": "copyToClipboard", "type": "bool", "default": false}, {"name": "audited", "type": "bool", "tooltip": true}, {"name": "readOnly", "type": "bool"}, {"name": "readOnlyAfterCreate", "type": "bool"}, {"name": "noSpellCheck", "type": "bool", "default": false, "hidden": true}], "validationList": ["required", "max<PERSON><PERSON><PERSON>", "pattern"], "mandatoryValidationList": ["max<PERSON><PERSON><PERSON>"], "sanitizerClassNameList": ["Espo\\Classes\\FieldSanitizers\\StringTrim"], "filter": true, "personalData": true, "textFilter": true, "textFilterForeign": true, "dynamicLogicOptions": true, "fullTextSearch": true, "default": null, "view": "text-hideable:views/fields/text-hideable"}