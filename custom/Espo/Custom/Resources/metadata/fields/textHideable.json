{"view": "custom:views/fields/text-hideable", "params": [{"name": "required", "type": "bool", "default": false}, {"name": "default", "type": "text"}, {"name": "max<PERSON><PERSON><PERSON>", "type": "int"}, {"name": "rows", "type": "int", "min": 1, "default": 4}, {"name": "rowsMin", "type": "int", "default": 2, "min": 1, "hidden": true}, {"name": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "type": "bool", "default": true, "tooltip": "textHideableHideByDefault"}, {"name": "showToggleButton", "type": "bool", "default": true, "tooltip": "textHideableShowToggleButton"}, {"name": "cutHeight", "type": "int", "default": 200, "min": 1, "tooltip": true}, {"name": "seeMoreDisabled", "type": "bool", "tooltip": true}, {"name": "displayRawText", "type": "bool"}, {"name": "readOnly", "type": "bool"}, {"name": "readOnlyAfterCreate", "type": "bool"}, {"name": "audited", "type": "bool", "tooltip": true}], "validationList": ["required", "max<PERSON><PERSON><PERSON>"], "filter": true, "personalData": true, "textFilter": true, "textFilterForeign": true, "fullTextSearch": true, "sanitizerClassNameList": ["Espo\\Classes\\FieldSanitizers\\EmptyStringToNull"], "fieldDefs": {"type": "text", "notNull": false}, "default": null}