<?php
/**
 * Installation script for Text Hideable field
 * Run this script after installing the field to clear cache and verify installation
 */

// Include EspoCRM bootstrap
require_once 'bootstrap.php';

$app = new \Espo\Core\Application();
$container = $app->getContainer();

echo "Installing Text Hideable Field...\n";

try {
    // Clear cache
    echo "Clearing cache...\n";
    $container->get('dataManager')->clearCache();
    
    // Rebuild
    echo "Rebuilding...\n";
    $container->get('dataManager')->rebuild();
    
    echo "✓ Text Hideable field installed successfully!\n";
    echo "\nNext steps:\n";
    echo "1. Go to Administration > Entity Manager\n";
    echo "2. Select an entity and add a new field\n";
    echo "3. Choose 'Text (Hideable)' as the field type\n";
    echo "4. Configure the field parameters as needed\n";
    echo "5. Save and rebuild\n";
    
} catch (Exception $e) {
    echo "✗ Error during installation: " . $e->getMessage() . "\n";
    exit(1);
}
