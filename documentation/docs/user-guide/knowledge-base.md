# Knowledge Base

The Knowledge Base provides the ability to create articles. Articles may contain information about your products, guidelines for clients, or help for internal users to improve their productivity.

To make an article available in the Portal, you need to set its status to *Published* and select the needed portal in the *Portals* field. Make sure that the Portal Role allows an access to the Knowledge Base scope and the navigation menu tab is added in the Portal.

If you specify an *Expiration Date*, then the article will be automatically hidden from the Portal on that date.

An order in which articles are listed can be manipulated by actions in the dropdown menu of a specific article.

Knowledge Base articles can be associated with Case records. This provides quick access to information from a Case record.

## Categories

Categories allow to group articles. One category can contain multiple sub-categories. Each article can be related to one or multiple categories.

The Knowledge Base Category is a separate entity type, hence access can be controlled by Roles.
