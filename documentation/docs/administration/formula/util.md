# Formula > Functions > Util

* [util\generateId](#utilgenerateid)
* [util\generateRecordId](#utilgeneraterecordid)
* [util\base64Encode](#utilbase64encode)
* [util\base64Decode](#utilbase64decode)

## util\generateId

Generates a unique ID. Returns a string.

!!! example

    ```
    $uniqueId = util\generateId();
    ```

## util\generateRecordId

*As of v7.5.*

Generates an ID that can be used as an ID for an entity. Returns a string.

!!! example

    ```
    $recordId = util\generateRecordId();
    ```

## util\base64Encode

*As of v8.3.*

Base64 encode.

## util\base64Decode

*As of v8.3.*

Base64 decode.
