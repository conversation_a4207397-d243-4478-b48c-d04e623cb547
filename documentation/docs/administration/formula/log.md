# Formula > Functions > Log

*As of v8.3.*

* [log\info](#loginfo)
* [log\notice](#lognotice)
* [log\warning](#logwarning)
* [log\error](#logerror)

## log\info

`log\info(MESSAGE, [CONTEXT])`

Log a string MESSAGE with the INFO level. An optional CONTEXT should be an object.

## log\notice

`log\notice(MESSAGE, [CONTEXT])`

Log a string MESSAGE with the NOTICE level. An optional CONTEXT should be an object.

## log\warning

`log\warning(MESSAGE, [CONTEXT])`

Log a string MESSAGE with the WARNING level. An optional CONTEXT should be an object.

## log\error

`log\error(MESSAGE, [CONTEXT])`

Log a string MESSAGE with the ERROR level. An optional CONTEXT should be an object.
