# Dashboards

All internal users can manage their dashboards: add or remove dashlets and tabs, edit dashlet options.

An administrator can edit dashboards of other users. On the user detail view, click the *Preferences* item from the dropdown at the top-right corner. There, you can find *Dashboard Layout*.

Users can **reset** their dashboard layout to the default. On the Preferences edit view, click the *Reset Dashboard to Default* item from the dropdown next to the *Edit* button. The administrator can reset dashboards of other users.

On the dashboard:

* Dashlets can be **dragged** by the header.
* Dashlets can be **resized** by dragging the lower-right corner.
* Multiple dashboard tabs can be created to group dashlets by their purpose.

## System default dashboard

The default dashboard layout will be applied to all new users. This layout be changed at Administration > User Interface > Dashboard Layout.

## Dashboard Templates

Available for administrators at Administration > Dashboard Templates. Dashboard Templates provide the ability to deploy specific dashboard layouts to multiple users. It's also possible to deploy for all users of a specific team.


## Portal dashboard

On the edit view of the Portal record, you can define its default dashboard layout. It will be applied to all portal users unless a user has a specific *Dashboard Template*.

When editing a *Portal User*, you can specify the Dashboard Template for that user. This template will override the default template of the Portal.

## See also

* [Creating custom dashlet](../development/how-to-create-a-dashlet.md)
