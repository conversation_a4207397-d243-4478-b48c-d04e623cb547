# Export Import extension

The Export Import extension is a tool for transferring data between EspoCRM instances using CLI commands.

What can be transferred:

* records
* settings
* customizations
* files

How it can be used:

* as a part of a continuous delivery pipeline (e.g. to transfer roles, workflows, BPM flowcharts from dev to production);
* for demo data;
* when migrating to another instance;
* to track changes;
* to restore updated or deleted records.

## Installing

You need to install the Export Import extension on your EspoCRM instance. Download the latest release package from the [GitHub repository](https://github.com/espocrm/ext-export-import/releases). Follow [instructions](https://docs.espocrm.com/administration/extensions/#installing) to install the extension.

## Features

* [Export](export.md)
* [Import](import.md)
* [Compare changes](compare.md)

## Additional information

* [Run by code](run-by-code.md)
* [Customization](customization.md)
