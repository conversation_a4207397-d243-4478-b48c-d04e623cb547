name,targetType,isActive,data,description
"Example: Email reply catching",Account,,"{""list"":[{""type"":""eventStart"",""center"":{""x"":100,""y"":160},""id"":""9y6izy64v1""},{""type"":""taskSendMessage"",""center"":{""x"":200,""y"":160},""doNotStore"":false,""from"":""system"",""to"":""targetEntity"",""replyTo"":"""",""messageType"":""Email"",""optOutLink"":false,""id"":""a1r9e3g1ee"",""text"":""Send email to account"",""emailTemplateName"":null,""emailTemplateId"":null,""description"":""Select email template.""},{""startId"":""9y6izy64v1"",""endId"":""a1r9e3g1ee"",""startDirection"":""r"",""id"":""63d18vn5hw"",""type"":""flow""},{""type"":""gatewayEventBased"",""center"":{""x"":300,""y"":160},""id"":""ximev2a5su"",""text"":null,""description"":""Stops the flow until the first subsequent event is triggered.""},{""startId"":""a1r9e3g1ee"",""endId"":""ximev2a5su"",""startDirection"":""r"",""id"":""eigxf6kf3b"",""type"":""flow""},{""type"":""eventIntermediateMessageCatch"",""center"":{""x"":380,""y"":160},""repliedTo"":""a1r9e3g1ee"",""relatedTo"":null,""messageType"":""Email"",""id"":""076bji878y"",""text"":""Replied with 'yes'"",""description"":""Event will be triggered if the received email is a reply to the email sent by the process before and body contains word 'yes'"",""conditionsFormula"":""string\\contains(body, string\\lowerCase('yes'))""},{""type"":""eventIntermediateTimerCatch"",""center"":{""x"":380,""y"":260},""timerBase"":null,""timerShift"":24,""timerShiftUnits"":""hours"",""timerShiftOperator"":""plus"",""timerFormula"":null,""id"":""5g9qccto4j"",""text"":""Wait 24h"",""description"":null},{""startId"":""ximev2a5su"",""endId"":""5g9qccto4j"",""startDirection"":""d"",""id"":""vzobuguik8"",""type"":""flow""},{""startId"":""ximev2a5su"",""endId"":""076bji878y"",""startDirection"":""r"",""id"":""ddeuegbdmn"",""type"":""flow""},{""type"":""eventEnd"",""center"":{""x"":480,""y"":260},""id"":""smpn4950he""},{""startId"":""5g9qccto4j"",""endId"":""smpn4950he"",""startDirection"":""r"",""id"":""w8cy6l5tja"",""type"":""flow""},{""type"":""task"",""center"":{""x"":480,""y"":160},""id"":""0st6bue5bz"",""text"":""Create task"",""description"":""You need also to assign the task to some user."",""actionList"":[{""link"":""Task"",""fieldList"":[""name""],""fields"":{""name"":{""subjectType"":""value"",""attributes"":{""name"":""Discuss about a possible deal""}}},""cid"":0,""id"":""27dhn9mrxs"",""linkList"":[""parent""],""formula"":"""",""entityType"":""Task"",""type"":""createEntity""}]},{""startId"":""076bji878y"",""endId"":""0st6bue5bz"",""startDirection"":""r"",""id"":""wwft7n4nzu"",""type"":""flow""},{""type"":""eventEnd"",""center"":{""x"":580,""y"":160},""id"":""znvksdm1ff""},{""startId"":""0st6bue5bz"",""endId"":""znvksdm1ff"",""startDirection"":""r"",""id"":""yqcx692xco"",""type"":""flow""}],""createdEntitiesData"":{""a1r9e3g1ee"":{""elementId"":""a1r9e3g1ee"",""actionId"":null,""entityType"":""Email"",""numberId"":0,""text"":""Send email to account""},""0st6bue5bz_27dhn9mrxs"":{""elementId"":""0st6bue5bz"",""actionId"":""27dhn9mrxs"",""link"":null,""entityType"":""Task"",""numberId"":0}}}","This example shows how it's possible to catch a reply to the email sent by the process.

* This flowchart is not active. You need to set *Is Active* to make it runnable.

Click on flow items bellow to see more info.

This process supposed to be started manually (from the account detail view > dropdown in top-right corner).
 
You can replace start event with the event of other type."
"Example: Sub-process",Account,,"{""list"":[{""type"":""eventStart"",""center"":{""x"":40,""y"":100},""id"":""dnce1dwk9c"",""text"":""Start manually"",""description"":null},{""type"":""task"",""center"":{""x"":140,""y"":100},""id"":""d0bwlpsqjb"",""text"":""Create opportunity"",""description"":null,""actionList"":[{""link"":""Opportunity"",""fieldList"":[""name"",""stage"",""closeDate"",""amount""],""fields"":{""name"":{""subjectType"":""value"",""attributes"":{""name"":""New subscription""}},""stage"":{""subjectType"":""value"",""attributes"":{""stage"":""Proposal""}},""closeDate"":{""subjectType"":""today"",""shiftDays"":""1"",""attributes"":{},""shiftUnit"":""months""},""amount"":{""subjectType"":""value"",""attributes"":{""amount"":500,""amountCurrency"":""USD""}}},""cid"":0,""id"":""mo1mt5e7q1"",""linkList"":[""account""],""formula"":"""",""entityType"":""Opportunity"",""type"":""createEntity""}]},{""startId"":""dnce1dwk9c"",""endId"":""d0bwlpsqjb"",""startDirection"":""r"",""id"":""tscf17xz0c"",""type"":""flow""},{""type"":""subProcess"",""center"":{""x"":240,""y"":260},""isExpanded"":true,""triggeredByEvent"":false,""dataList"":[{""type"":""eventStart"",""center"":{""x"":40,""y"":80},""id"":""rfa7a1ten5"",""text"":null,""description"":""The flow starts from Start Event once Sub-Process element is reached by the flow.""},{""type"":""task"",""center"":{""x"":140,""y"":80},""id"":""fwdhod2w40"",""text"":""Assign opportunity"",""actionList"":[],""description"":""Here you can add 'Apply Assignment Rule' or 'Update Target Record' action.""},{""startId"":""rfa7a1ten5"",""endId"":""fwdhod2w40"",""startDirection"":""r"",""id"":""nk0635f6o5"",""type"":""flow""},{""type"":""eventIntermediateConditionalCatch"",""center"":{""x"":260,""y"":80},""id"":""ya9l302iso"",""text"":""Opportunity is won"",""conditionsAll"":[{""comparison"":""equals"",""subjectType"":""value"",""cid"":0,""fieldToCompare"":""stage"",""type"":""all"",""value"":""Closed Won""}],""conditionsAny"":[],""conditionsFormula"":"""",""description"":null},{""type"":""eventEnd"",""center"":{""x"":340,""y"":80},""id"":""nj9rtq4sxs""},{""startId"":""ya9l302iso"",""endId"":""nj9rtq4sxs"",""startDirection"":""r"",""id"":""71idx7lyd3"",""type"":""flow""},{""startId"":""fwdhod2w40"",""endId"":""ya9l302iso"",""startDirection"":""r"",""id"":""o695oc3hqy"",""type"":""flow""}],""target"":""created:d0bwlpsqjb_mo1mt5e7q1"",""returnVariableList"":[],""targetType"":""Opportunity"",""id"":""d5fxlnoewa"",""width"":395,""height"":184,""text"":""Target is switched to opportunity within sub-process"",""description"":null},{""startId"":""d0bwlpsqjb"",""endId"":""d5fxlnoewa"",""startDirection"":""r"",""id"":""eozfn81pez"",""type"":""flow""},{""type"":""eventIntermediateTimerBoundary"",""attachedToId"":""d5fxlnoewa"",""cancelActivity"":false,""timerBase"":null,""timerShift"":10,""timerShiftUnits"":""days"",""timerShiftOperator"":""plus"",""timerFormula"":null,""attachPosition"":""bl4"",""center"":{""x"":100,""y"":352},""id"":""bwf5gnjmh2"",""text"":""10 days passed"",""description"":""If 10 days passed and sub-process is still active then this event will be triggered. Since it is not interrupting, it won't stop the sub-process.""},{""type"":""task"",""center"":{""x"":100,""y"":460},""id"":""qd6ismf2r1"",""text"":""Notify user assigned to account"",""actionList"":[{""recipient"":""link:assignedUser"",""userIdList"":[],""userNames"":{},""cid"":0,""id"":""cccgw7wd87"",""messageTemplate"":""Opportunity for account {entity} has been in process for 10 days."",""specifiedTeamsIds"":[],""specifiedTeamsNames"":{},""type"":""createNotification""}],""description"":null},{""type"":""eventEnd"",""center"":{""x"":100,""y"":540},""id"":""bzuaili08f"",""text"":null,""description"":""This will NOT stop the process, because there will still active flow items. It's a best practice to end any flow with End Event.""},{""startId"":""qd6ismf2r1"",""endId"":""bzuaili08f"",""startDirection"":""d"",""id"":""yx8275wtrt"",""type"":""flow""},{""startId"":""bwf5gnjmh2"",""endId"":""qd6ismf2r1"",""startDirection"":""d"",""id"":""4j2hubewis"",""type"":""flow""},{""type"":""eventIntermediateConditionalBoundary"",""attachedToId"":""d5fxlnoewa"",""cancelActivity"":true,""attachPosition"":""bl1"",""center"":{""x"":220,""y"":352},""id"":""i7rer4ire8"",""text"":""Opportunity is lost"",""conditionsAll"":[{""comparison"":""equals"",""subjectType"":""value"",""cid"":0,""fieldToCompare"":""created:d0bwlpsqjb_mo1mt5e7q1.stage"",""type"":""all"",""value"":""Closed Lost""}],""conditionsAny"":[],""conditionsFormula"":"""",""description"":""It's an interrupting event. It will stop the sub-process it's attached to.""},{""type"":""task"",""center"":{""x"":220,""y"":460},""id"":""t30fe2rbnm"",""text"":""Notify user assigned to account"",""actionList"":[{""recipient"":""link:assignedUser"",""userIdList"":[],""userNames"":{},""cid"":0,""id"":""qstbhs9m8a"",""messageTemplate"":""Opportunity for account {entity} is lost."",""specifiedTeamsIds"":[],""specifiedTeamsNames"":{},""type"":""createNotification""}],""description"":null},{""startId"":""i7rer4ire8"",""endId"":""t30fe2rbnm"",""startDirection"":""d"",""id"":""l0wsiyzsbo"",""type"":""flow""},{""type"":""eventEnd"",""center"":{""x"":220,""y"":540},""id"":""m6krciw1ab"",""text"":null,""description"":""The process will be stopped with this event, since there won't be any active flow items at that moment. It's a best practice to end any flow with End Event.""},{""startId"":""t30fe2rbnm"",""endId"":""m6krciw1ab"",""startDirection"":""d"",""id"":""lgt4082clq"",""type"":""flow""},{""type"":""task"",""center"":{""x"":520,""y"":260},""id"":""r3co6n6vyx"",""text"":""Create task to arrange shipment"",""actionList"":[{""link"":""Task"",""fieldList"":[""name""],""fields"":{""name"":{""subjectType"":""value"",""attributes"":{""name"":""Arrange shipment""}}},""cid"":0,""id"":""eatnd0zhrz"",""linkList"":[""account""],""formula"":"""",""entityType"":""Task"",""type"":""createEntity""}],""description"":""You can make this task assigned to some used. Edit the action and add Assigned User field.""},{""startId"":""d5fxlnoewa"",""endId"":""r3co6n6vyx"",""startDirection"":""r"",""id"":""3ktu0s122l"",""type"":""flow""},{""type"":""eventEnd"",""center"":{""x"":620,""y"":260},""id"":""oxcrq8pqoh""},{""startId"":""r3co6n6vyx"",""endId"":""oxcrq8pqoh"",""startDirection"":""r"",""id"":""f7nkp9lajf"",""type"":""flow""}],""createdEntitiesData"":{""d0bwlpsqjb_mo1mt5e7q1"":{""elementId"":""d0bwlpsqjb"",""actionId"":""mo1mt5e7q1"",""link"":null,""entityType"":""Opportunity"",""numberId"":0},""r3co6n6vyx_eatnd0zhrz"":{""elementId"":""r3co6n6vyx"",""actionId"":""eatnd0zhrz"",""link"":null,""entityType"":""Task"",""numberId"":0}}}","This example shows how it's regular sub-processes work in BPM.

* This flowchart is not active. You need to set *Is Active* to make it runnable.

Click on flow items bellow to see more info.

This process supposed to be started manually (from the account detail view > dropdown in top-right corner).
 
You can replace start event with the event of other type."
"Example: User task",Lead,,"{""list"":[{""type"":""taskUser"",""center"":{""x"":160,""y"":120},""actionType"":""Approve"",""assignmentType"":"""",""instructions"":null,""name"":""Approve lead: '{$name}'"",""targetTeamId"":null,""target"":"""",""id"":""450yvismmk"",""text"":""User needs to approve lead"",""targetTeamName"":null,""description"":""You need to specify *Assignment* field. E.g. you can make the task to be assigned to a specific user, who is a sales manager or apply round-robin among users of a specific team.\n\nThe execution of the flow will be stopped until the user task is resolved.\n\n""},{""type"":""eventStartConditional"",""center"":{""x"":60,""y"":120},""triggerType"":""afterRecordCreated"",""isInterrupting"":false,""id"":""hmchah0hs2"",""text"":""Lead created & status is 'New'"",""description"":null,""conditionsAll"":[{""comparison"":""equals"",""subjectType"":""value"",""cid"":0,""fieldToCompare"":""status"",""value"":""New"",""type"":""all""}],""conditionsAny"":[],""conditionsFormula"":""""},{""startId"":""hmchah0hs2"",""endId"":""450yvismmk"",""startDirection"":""r"",""id"":""4xiy8mazdh"",""type"":""flow""},{""type"":""gatewayExclusive"",""center"":{""x"":260,""y"":120},""id"":""kw4z4p00jg"",""text"":null,""defaultFlowId"":""p0md4yzc4c"",""flowList"":[{""id"":""m4hu3o0ykw"",""conditionsAll"":[{""comparison"":""equals"",""subjectType"":""value"",""cid"":0,""fieldToCompare"":""created:450yvismmk.resolution"",""value"":""Approved"",""type"":""all""}],""conditionsAny"":[],""conditionsFormula"":""""}],""description"":null},{""startId"":""450yvismmk"",""endId"":""kw4z4p00jg"",""startDirection"":""r"",""id"":""zvzlrejg7j"",""type"":""flow""},{""type"":""task"",""center"":{""x"":380,""y"":180},""id"":""ftj2vk8jyg"",""text"":""Change status to 'Dead'"",""actionList"":[{""fieldList"":[""status""],""fields"":{""status"":{""subjectType"":""value"",""attributes"":{""status"":""Dead""}}},""cid"":0,""id"":""uapdcjlwh3"",""formula"":"""",""type"":""updateEntity""}],""description"":null},{""startId"":""kw4z4p00jg"",""endId"":""ftj2vk8jyg"",""startDirection"":""d"",""id"":""p0md4yzc4c"",""type"":""flow"",""isDefault"":true,""text"":""not"",""description"":null},{""type"":""eventEnd"",""center"":{""x"":480,""y"":180},""id"":""sbye7srr81"",""text"":null,""description"":null},{""startId"":""ftj2vk8jyg"",""endId"":""sbye7srr81"",""startDirection"":""r"",""id"":""0rzdao21lz"",""type"":""flow""},{""type"":""task"",""center"":{""x"":380,""y"":60},""id"":""dro3qss4io"",""text"":""Assign lead, change status to 'Assigned'"",""actionList"":[{""fieldList"":[""status""],""fields"":{""status"":{""subjectType"":""value"",""attributes"":{""status"":""Assigned""}}},""cid"":0,""id"":""re6s604de8"",""formula"":"""",""type"":""updateEntity""}],""description"":""Add *Apply Assigned Rule* action or use *Update Target Record* to set the specific Assigned User.""},{""startId"":""kw4z4p00jg"",""endId"":""dro3qss4io"",""startDirection"":""u"",""id"":""m4hu3o0ykw"",""type"":""flow"",""isDefault"":false,""text"":""approved"",""description"":null},{""type"":""eventEnd"",""center"":{""x"":480,""y"":60},""id"":""7gp8swt3c2"",""text"":null,""description"":null},{""startId"":""dro3qss4io"",""endId"":""7gp8swt3c2"",""startDirection"":""r"",""id"":""l0kizq3kbg"",""type"":""flow""}],""createdEntitiesData"":{""450yvismmk"":{""elementId"":""450yvismmk"",""actionId"":null,""entityType"":""BpmnUserTask"",""numberId"":0,""text"":""User needs to approve lead""}}}","This example shows how it's possible to make BPM process to interact with a user.

* This flowchart is not active. You need to set *Is Active* to make it runnable. 

Click on flow items bellow to see more info.

This example is for Contact entity type. You can create a similar flowchart for any other entity type.

"
"Example: Tracking URLs",Contact,,"{""list"":[{""type"":""eventStart"",""center"":{""x"":60,""y"":120},""id"":""kabctmad37"",""text"":""Start event"",""description"":""You can start a process manually, from the contact detail view > menu in the top-right corner.\n\nYou can use *Conditional Start Event* instead of this one. To start the process automatically once a contact meets a specific criteria.""},{""type"":""taskSendMessage"",""center"":{""x"":160,""y"":120},""doNotStore"":false,""from"":""system"",""to"":""targetEntity"",""replyTo"":"""",""messageType"":""Email"",""optOutLink"":false,""id"":""zlq87fmo83"",""text"":""Send tracking link to customer"",""emailTemplateName"":null,""emailTemplateId"":null,""description"":""Create and select an email template. Use a placeholder of your tracking URL in the template body as URL of a link.""},{""startId"":""kabctmad37"",""endId"":""zlq87fmo83"",""startDirection"":""r"",""id"":""s82mkwpspe"",""type"":""flow""},{""type"":""gatewayEventBased"",""center"":{""x"":280,""y"":120},""id"":""vcon04sppd"",""text"":null,""description"":""This will stop execution of the flow until any subsequent event is triggered.""},{""startId"":""zlq87fmo83"",""endId"":""vcon04sppd"",""startDirection"":""r"",""id"":""6weyw6rep4"",""type"":""flow""},{""type"":""eventIntermediateTimerCatch"",""center"":{""x"":360,""y"":120},""timerBase"":null,""timerShift"":7,""timerShiftUnits"":""days"",""timerShiftOperator"":""plus"",""timerFormula"":null,""id"":""hy7u5o7agx"",""text"":""Time expired"",""description"":""We 7 days expire, the event will be triggered. It will cancel the concurrent pending event 'Link clicked'.""},{""type"":""eventIntermediateSignalCatch"",""center"":{""x"":360,""y"":220},""signal"":""clickUrl.Contact.{$id}.5d8206aa9d76df4c8"",""id"":""isu1v13nfx"",""text"":""Link clicked"",""description"":""This event is triggered once a customer clicked the link.\n\nReplace `5d8206aa9d76df4c8` with the real ID of your tracking URL. ID can be obtained from the address bar or from the placeholder of the tracking URL.""},{""startId"":""vcon04sppd"",""endId"":""hy7u5o7agx"",""startDirection"":""r"",""id"":""fxvfwqph8o"",""type"":""flow""},{""startId"":""vcon04sppd"",""endId"":""isu1v13nfx"",""startDirection"":""d"",""id"":""7fjh4skior"",""type"":""flow""},{""type"":""eventEnd"",""center"":{""x"":440,""y"":120},""id"":""oii6la4bfz"",""text"":null,""description"":null},{""type"":""task"",""center"":{""x"":460,""y"":220},""id"":""y68y98mltp"",""text"":""Do something"",""description"":""Here you can make some actions with the contact. E.g. update some field with *Update Target Record* action."",""actionList"":[]},{""type"":""eventEnd"",""center"":{""x"":560,""y"":220},""id"":""wqo430vyvs"",""text"":null,""description"":null},{""startId"":""hy7u5o7agx"",""endId"":""oii6la4bfz"",""startDirection"":""r"",""id"":""0yxkcxh31f"",""type"":""flow""},{""startId"":""isu1v13nfx"",""endId"":""y68y98mltp"",""startDirection"":""r"",""id"":""hq9xnk35rt"",""type"":""flow""},{""startId"":""y68y98mltp"",""endId"":""wqo430vyvs"",""startDirection"":""r"",""id"":""xm0tnpk22u"",""type"":""flow""}],""createdEntitiesData"":{""zlq87fmo83"":{""elementId"":""zlq87fmo83"",""actionId"":null,""entityType"":""Email"",""numberId"":0,""text"":""Send tracking link to customer""}}}","This example shows how it's possible to automatically interact with a customer via emails.

* This flowchart is not active. You need to set *Is Active* to make it runnable.
* You need to create Tracking URL at Campaigns > top-right menu > Tracking URLs.  Tracking URLs. Create URL and obtain a generated placeholder (example: `{trackingUrl:5d8206aa9d76df4c8}`). Use that placeholder as a URL of the link in your email template. Example: `<a href=""{trackingUrl:5d8206aa9d76df4c8}"">Click me</a>`.

Click on flow items bellow to see more info.

This example is for Contact entity type. You can create a similar flowchart for Account or Lead.

"
"Example: Drip email campaign",Lead,,"{""list"":[{""type"":""eventStartSignal"",""center"":{""x"":40,""y"":100},""signal"":""@leadCapture.LEAD_CAPTURE_ID"",""isInterrupting"":false,""id"":""tj7y8xjxel"",""text"":""Lead subscribed"",""description"":""Replace *LEAD_CAPTURE_ID* with ID for the Lead Capture record. \n\nID can be obtained from the address bar on the detail view of Lead Capture record. Administration > Lead Capture > click on the record.\n\nYou can also start the process manually, from the lead detail view > menu in the top-right corner.\n\nYou can also use *Conditional Start Event* instead of this one. To start the campaign once a lead meets a specific criteria.""},{""type"":""taskSendMessage"",""center"":{""x"":140,""y"":100},""doNotStore"":false,""from"":""system"",""to"":""targetEntity"",""replyTo"":"""",""messageType"":""Email"",""optOutLink"":true,""id"":""vv3g2qreo7"",""text"":""Send welcome email"",""emailTemplateName"":null,""emailTemplateId"":null,""description"":""Select email template. ""},{""startId"":""tj7y8xjxel"",""endId"":""vv3g2qreo7"",""startDirection"":""r"",""id"":""18tm84gton"",""type"":""flow""},{""type"":""eventIntermediateTimerCatch"",""center"":{""x"":240,""y"":100},""timerBase"":null,""timerShift"":7,""timerShiftUnits"":""days"",""timerShiftOperator"":""plus"",""timerFormula"":null,""id"":""nl6fasw5ey"",""text"":""Wait 7 days"",""description"":""This will stop a flow execution for 7 days.""},{""startId"":""vv3g2qreo7"",""endId"":""nl6fasw5ey"",""startDirection"":""r"",""id"":""katp0qxsd7"",""type"":""flow""},{""type"":""taskSendMessage"",""center"":{""x"":280,""y"":200},""doNotStore"":false,""from"":""system"",""to"":""targetEntity"",""replyTo"":"""",""messageType"":""Email"",""optOutLink"":true,""id"":""35pbf4obcs"",""text"":""Send email #1"",""emailTemplateName"":null,""emailTemplateId"":null,""description"":""Select email template.""},{""startId"":""nl6fasw5ey"",""endId"":""35pbf4obcs"",""startDirection"":""r"",""id"":""0v29rrcw0z"",""type"":""flow""},{""type"":""eventIntermediateTimerCatch"",""center"":{""x"":380,""y"":200},""timerBase"":null,""timerShift"":7,""timerShiftUnits"":""days"",""timerShiftOperator"":""plus"",""timerFormula"":null,""id"":""tvzgqpzarn"",""text"":""Wait 7 days"",""description"":""This will stop a flow execution for 7 days.""},{""startId"":""35pbf4obcs"",""endId"":""tvzgqpzarn"",""startDirection"":""r"",""id"":""rs87q5e62u"",""type"":""flow""},{""type"":""gatewayExclusive"",""center"":{""x"":460,""y"":260},""id"":""qul8pya34q"",""text"":""Lead is converted"",""description"":null,""defaultFlowId"":""la5nv59bb1"",""flowList"":[{""id"":""ztv11gwdiu"",""conditionsAll"":[{""comparison"":""equals"",""subjectType"":""value"",""cid"":0,""fieldToCompare"":""status"",""value"":""Converted"",""type"":""all""}],""conditionsAny"":[],""conditionsFormula"":""""}]},{""startId"":""tvzgqpzarn"",""endId"":""qul8pya34q"",""startDirection"":""r"",""id"":""ylwrry53ss"",""type"":""flow""},{""type"":""taskSendMessage"",""center"":{""x"":560,""y"":320},""doNotStore"":false,""from"":""system"",""to"":""targetEntity"",""replyTo"":"""",""messageType"":""Email"",""optOutLink"":true,""id"":""jnkhc4jx17"",""text"":""Send email #2-c"",""emailTemplateName"":null,""emailTemplateId"":null,""description"":""Select email template. ""},{""type"":""taskSendMessage"",""center"":{""x"":360,""y"":320},""doNotStore"":false,""from"":""system"",""to"":""targetEntity"",""replyTo"":"""",""messageType"":""Email"",""optOutLink"":true,""id"":""z614240ac9"",""text"":""Send email #2"",""emailTemplateName"":null,""emailTemplateId"":null,""description"":""Select email template. ""},{""startId"":""qul8pya34q"",""endId"":""jnkhc4jx17"",""startDirection"":""r"",""id"":""ztv11gwdiu"",""type"":""flow"",""isDefault"":false,""text"":""yes"",""description"":null},{""startId"":""qul8pya34q"",""endId"":""z614240ac9"",""startDirection"":""l"",""id"":""la5nv59bb1"",""type"":""flow"",""isDefault"":true,""text"":""no"",""description"":null},{""type"":""gatewayExclusive"",""center"":{""x"":460,""y"":380},""id"":""8td5tjelmv""},{""startId"":""jnkhc4jx17"",""endId"":""8td5tjelmv"",""startDirection"":""d"",""id"":""cv25n3sry1"",""type"":""flow""},{""startId"":""z614240ac9"",""endId"":""8td5tjelmv"",""startDirection"":""d"",""id"":""ylupvbttcf"",""type"":""flow""},{""type"":""eventIntermediateTimerCatch"",""center"":{""x"":460,""y"":460},""timerBase"":null,""timerShift"":30,""timerShiftUnits"":""days"",""timerShiftOperator"":""plus"",""timerFormula"":null,""id"":""1st7mkgevh"",""text"":""Wait 30 days"",""description"":null},{""startId"":""8td5tjelmv"",""endId"":""1st7mkgevh"",""startDirection"":""d"",""id"":""avlmd4qzcy"",""type"":""flow""},{""type"":""taskSendMessage"",""center"":{""x"":560,""y"":460},""doNotStore"":false,""from"":""system"",""to"":""targetEntity"",""replyTo"":"""",""messageType"":""Email"",""optOutLink"":true,""id"":""wxpeoqmqto"",""text"":""Send email #3"",""emailTemplateName"":null,""emailTemplateId"":null,""description"":""Select email template. ""},{""startId"":""1st7mkgevh"",""endId"":""wxpeoqmqto"",""startDirection"":""r"",""id"":""m0jfvpcul9"",""type"":""flow""},{""type"":""task"",""center"":{""x"":560,""y"":560},""id"":""g5j1ksct0e"",""text"":""Update lead"",""description"":""Here you can update the lead record. For example: set some field to indicate that the lead has finished the campaign.\n\nEdit *Update Target Record* action to specify what to update."",""actionList"":[{""fieldList"":[],""fields"":{},""cid"":0,""id"":""y7s7rqp9rx"",""formula"":"""",""type"":""updateEntity""}]},{""startId"":""wxpeoqmqto"",""endId"":""g5j1ksct0e"",""startDirection"":""d"",""id"":""x9hwxkda4m"",""type"":""flow""},{""type"":""eventEnd"",""center"":{""x"":660,""y"":560},""id"":""8irjncf90g"",""text"":""End campaign"",""description"":null},{""startId"":""g5j1ksct0e"",""endId"":""8irjncf90g"",""startDirection"":""r"",""id"":""rb39zoq635"",""type"":""flow""},{""type"":""eventSubProcess"",""center"":{""x"":100,""y"":360},""isExpanded"":true,""triggeredByEvent"":true,""dataList"":[{""type"":""eventStartSignal"",""center"":{""x"":40,""y"":40},""signal"":""optOut.Lead.{$id}"",""isInterrupting"":true,""id"":""g2r3zm7c5m"",""text"":""Lead opted-out"",""description"":""Do NOT change the signal name above.\n\nThis event is interrupting, meaning it will stop the parent process once triggered, so the campaign will be stopped.""},{""type"":""eventEnd"",""center"":{""x"":140,""y"":40},""id"":""9ezcdbezdj""},{""startId"":""g2r3zm7c5m"",""endId"":""9ezcdbezdj"",""startDirection"":""r"",""id"":""4pz5fd6hh5"",""type"":""flow""}],""target"":"""",""targetType"":""Lead"",""id"":""pq3dd59hsq"",""eventStartData"":{""type"":""eventStartSignal"",""center"":{""x"":60,""y"":40},""signal"":""optOut.Lead.{$id}"",""isInterrupting"":true,""id"":""g2r3zm7c5m"",""text"":""Lead opted-out"",""description"":""Do NOT change the signal name above.\n\nThis event is interrupting, meaning it will stop the parent process once triggered, so the campaign will be stopped.""},""width"":189,""height"":121}],""createdEntitiesData"":{""vv3g2qreo7"":{""elementId"":""vv3g2qreo7"",""actionId"":null,""entityType"":""Email"",""numberId"":0,""text"":""Send welcome email""},""35pbf4obcs"":{""elementId"":""35pbf4obcs"",""actionId"":null,""entityType"":""Email"",""numberId"":1,""text"":""Send email #1""},""jnkhc4jx17"":{""elementId"":""jnkhc4jx17"",""actionId"":null,""entityType"":""Email"",""numberId"":2,""text"":""Send email #2-c""},""z614240ac9"":{""elementId"":""z614240ac9"",""actionId"":null,""entityType"":""Email"",""numberId"":3,""text"":""Send email #2""},""wxpeoqmqto"":{""elementId"":""wxpeoqmqto"",""actionId"":null,""entityType"":""Email"",""numberId"":4,""text"":""Send email #3""}}}","This example show how it's possible to run drip campaigns with BPM.

* This flowchart is not active. You need to set *Is Active* to make it runnable.
* You need to edit Start Event to specify ID of a lead capture record.
* You need to specify email templates for each 'Send Message' tasks.

Click on flow items bellow to see more info."
