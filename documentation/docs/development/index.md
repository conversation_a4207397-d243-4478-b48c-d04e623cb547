# Developer Documentation

### General

* [Getting started](how-to-start.md)
* [Making extension package](extension-packages.md)
* [Modules](modules.md)
* [Tests](tests.md)
* [Translation](translation.md)
* [Coding rules](coding-rules.md)

### Backend

* [Dependency injection](di.md)
* [Metadata](metadata.md)
* [ORM](orm.md)
* [Select Builder](select-builder.md)
* [API actions](api-action.md)
* [Services](services.md)
* [Hooks](hooks.md)
* [ACL](acl.md)
* [Entry points](entry-points.md)
* Misc
  * [Autoload](autoload.md)
  * [Entity type](custom-entity-type.md)
  * [Container services](container-services.md)
  * [Template helpers (PDF)](template-custom-helper.md)
  * [Formula functions](new-function-in-formula.md)
  * [Scheduled jobs](scheduled-job.md)
  * [Duplicate checking](duplicate-check.md)
  * [Database indexes](db-indexes.md)
  * [App params](app-params.md)
  * [Jobs](jobs.md)
  * [Email sending](email-sending.md)
  * [Calculated fields](calculated-fields.md)
  * [Config parameters](custom-config-parameters.md)
  * [Attachments](attachments.md)

### Frontend

* [View](view.md)
* [Model](model.md)
* [Collection](collection.md)
* [Templates](frontend/templates.md)
* [HTML & CSS](frontend/html-css.md)
* [Ajax requests](frontend/ajax.md)
* [Controller & routing](frontend/controller.md)
* [Dependency injection](frontend/dependency-injection.md)
* [Modal dialogs](modal.md)
* [Confirmation dialogs](confirm-dialog.md)
* [Custom views (for records and fields)](custom-views.md)
* [View setup handlers](frontend/view-setup-handlers.md)
* [Save error handlers](frontend/save-error-handlers.md)
* [Dynamic forms with dynamic handler](dynamic-handler.md)
* Fields
  * [Custom field type](custom-field-type.md)
  * [Customizing existing fields](customize-standard-fields.md)
* Misc
  * [Buttons & dropdown actions for detail/edit/list views](custom-buttons.md)
  * [Custom panels on record view](frontend/record-panels.md)
  * [Including custom CSS file](custom-css.md)
  * [Custom dashlets](how-to-create-a-dashlet.md)  
  * [Link-multiple field with primary record](link-multiple-with-primary.md)
  * Campaigns
    * [Custom unsubscribe page](campaign-unsubscribe-template.md)


### API

* [API Overview](api.md)
