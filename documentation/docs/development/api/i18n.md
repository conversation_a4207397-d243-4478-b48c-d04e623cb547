# API :: I18n (Internalization)

## Getting all labels

`GET I18n`

Returns all application labels. If authorized as an API User, labels will be translated to the default language (set in Administration > System). If authorized as a regular user (via Basic Authentication), then labels will be translated to the language set in preferences of that user. By passing GET parameter `default=true` it will force to return in the default language.

Response data:

* entity types and scopes: "Global" > "scopeNames" & "Global" > "scopeNamesPlural"
* field labels: EntityType > "fields"
* link labels: EntityType > "links"
