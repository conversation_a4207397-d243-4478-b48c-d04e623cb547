# Email Sending

Example:

```php
<?php
namespace Espo\SomeNamespace;

use Espo\Core\Mail\EmailSender;
use Espo\Core\Mail\EmailFactory;
use Espo\Core\Mail\SmtpParams;

class SomeClass
{   
    public function __construct(
        private EmailSender $emailSender,
        private EmailFactory $emailFactory
    ) {}
    
    public function send(): void
    {
        $email = $this->emailFactory->create();
        
        $email->setSubject('Subject');
        $email->setBody('Email Body');
        $email->addToAddress('<EMAIL>');
        $email->setFromAddress('<EMAIL>'); // optional; if not specified, the system address will be used
        $email->setIsPlain(); // Html is by default
        
        $smtpParams = SmtpParams::create(); // build SMTP params (optionally)

        $this->emailSender
            ->withSmtpParams($smtpParams) // optional
            ->withAttachments([$attachment]) // optional, to send with attachments
            ->send($email);
    }
}
```
