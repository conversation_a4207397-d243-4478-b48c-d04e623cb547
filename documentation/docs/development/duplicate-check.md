# Custom duplicate check

Duplicate check is performed upon record creation. Optionally, it can be enabled for record update.

*As of v7.0.*

You need to define a class name in metadata: recordDefs > {entityType} > duplicateWhereBuilderClassName. The class should implement `Espo\Core\Duplicate\WhereBuilder` interface.

### Example

Create `custom/Espo/Custom/Resources/metadata/recordDefs/Lead.json`:

```json
{
    "duplicateWhereBuilderClassName": "Espo\\Custom\\Classes\\DuplicateWhereBuilders\\Lead"
}
```

Create a file `custom/Espo/Custom/Classes/DuplicateWhereBuilders/Lead.php`:

```php
<?php
namespace Espo\Custom\Classes\DuplicateWhereBuilders;

use Espo\Core\Duplicate\WhereBuilder;

use Espo\ORM\Query\Part\Condition as Cond;
use Espo\ORM\Query\Part\WhereItem;
use Espo\ORM\Query\Part\Where\OrGroup;
use Espo\ORM\Entity;

class Lead implements WhereBuilder
{
    public function build(Entity $entity): ?WhereItem
    {
        $orBuilder = OrGroup::createBuilder();
        
        $toCheck = false;

        if ($entity->get('firstName') || $entity->get('lastName')) {
            $orBuilder->add(
                Cond::and(
                    Cond::equal(
                        Cond::column('firstName'),
                        $entity->get('firstName')
                    ),
                    Cond::equal(
                        Cond::column('lastName'),
                        $entity->get('lastName')
                    )
                )
            );

            $toCheck = true;
        }
        
        // Here you can add more conditions.
        
        if (!$toCheck) {
            return null;
        }

        return $orBuilder->build();
    }
}
```

You can find built-in duplicate where-builders [here](https://github.com/espocrm/espocrm/tree/master/application/Espo/Classes/DuplicateWhereBuilders). You can reuse them for your entity.

## Checking for duplicates when update

Disabled by default. Can be enabled in metadata: recordDefs > {entityType} > updateDuplicateCheck.

```json
{
    "updateDuplicateCheck": true
}
```
