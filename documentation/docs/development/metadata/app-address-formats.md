# app > addressFormats

A mapping format => params. Defines address formats (used in the backend).

## formatterClassName

*class-string<Espo\Core\Field\Address\AddressFormatter\>*

A formatter class. Should implement `Espo\Core\Field\Address\AddressFormatter` interface.

Example:

```json
{
    "1": {
        "formatterClassName": "Espo\\Classes\\AddressFormatters\\Formatter1"
    },
    "2": {
        "formatterClassName": "Espo\\Classes\\AddressFormatters\\Formatter2"
    },
    "3": {
        "formatterClassName": "Espo\\Classes\\AddressFormatters\\Formatter3"
    },
    "4": {
        "formatterClassName": "Espo\\Classes\\AddressFormatters\\Formatter4"
    }
}
```
