# app > databasePlatforms

Path: metadata > app > databasePlatforms.

*Object.<string, Object\>*

*As of v7.4.*

Example:

```json
{
    "Mysql": {},
    "Postgresql": {}
}
```

## dbalConnectionFactoryClass

*class-string<Espo\Core\Utils\Database\Dbal\ConnectionFactory\>*

## detailsProviderClassName

*class-string<Espo\Core\Utils\Database\Dbal\DetailsProvider\>*

## dbalTypeClassNameMap

*Object.<string, class-string<Doctrine\DBAL\Types\Type\>\>*

## indexHelperClassName

*class-string<Espo\Core\Utils\Database\Orm\IndexHelper\>*

## columnPreparatorClassName

*class-string<Espo\Core\Utils\Database\Schema\ColumnPreparator\>*

## preRebuildActionClassNameList

*class-string<Espo\Core\Utils\Database\Schema\RebuildAction\>[]*

## postRebuildActionClassNameList

*class-string<Espo\Core\Utils\Database\Schema\RebuildAction\>[]*
