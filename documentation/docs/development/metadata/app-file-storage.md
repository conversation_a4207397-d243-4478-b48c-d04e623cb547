# app > fileStorage

Path: metadata > app > fileStorage.

File storages.

## implementationClassNameMap

*Object.<string, class-string<Espo\Core\FileStorage\Storage\>\>*

Implementation classes for file storages.

Example:

``` json
{
    "implementationClassNameMap": {
        "EspoUploadDir": "Espo\\Core\\FileStorage\\Storages\\EspoUploadDir",
        "AwsS3": "Espo\\Core\\FileStorage\\Storages\\AwsS3"
    }
}
```
