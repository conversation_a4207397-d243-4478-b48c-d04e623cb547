# app > select

Path: metadata > app > select

Select framework definitions.

## boolFilterClassNameMap

*Object.<string, class-string<Espo\Core\Select\Bool\Filter\>\>*

Bool filters available for all entity types.

## orderItemConverterClassNameMap

*Object.<string, class-string<Espo\Core\Select\Order\ItemConverter\>\>*

Order item converters for field types.

## whereItemConverterClassNameMap

*Object.<string, class-string<Espo\Core\Select\Where\ItemConverter\>\>*

Where item converters.
