# app > file

Path: metadata > app > file.

## extensionMimeTypeMap

*Object.<string, string[]\>*

Mime types associated with file extensions.

Example:

```json
{
    "extensionMimeTypeMap": {
        "aac": ["audio/aac"]
    }
}
```

## inlineMimeTypeList

*string[]*

Mime types that can be downloaded as inline attachments.

Example:

```json
{
    "inlineMimeTypeList": [
        "__APPEND__",
        "application/some-type"
    ]
}
```
