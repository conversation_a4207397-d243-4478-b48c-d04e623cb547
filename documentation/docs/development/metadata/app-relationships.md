# app > relationships

Path: metadata > app > relationships.

*Object.<string, Object\>*

Definitions of specific named relationships.

*As of v7.4.*

Example:

```json
{
    "attachments": {
        "converterClassName": "Espo\\Core\\Utils\\Database\\Orm\\LinkConverters\\Attachments"
    }
}
```

## converterClassName

*class-string<Espo\Core\Utils\Database\Orm\LinkConverter\>*

Converts metadata link definitions to ORM definitions.
