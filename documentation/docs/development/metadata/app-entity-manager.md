# app > entityManager

Path: metadata > app > entityManager.


## createHookClassNameList

*class-string<Espo\Tools\EntityManager\Hook\CreateHook\>[]*

*As of v8.0.*


Hooks processed when a new custom entity type is created.


## deleteHookClassNameList

*class-string<Espo\Tools\EntityManager\Hook\DeleteHook\>[]*

*As of v8.0.*

Hooks processed when a custom entity type is deleted.

## updateHookClassNameList

*class-string<Espo\Tools\EntityManager\Hook\UpdateHook\>[]*

*As of v8.0.*

Hooks processed when parameters of an entity type are updated.
