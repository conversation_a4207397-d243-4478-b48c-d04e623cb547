# app > fieldProcessing

Path: metadata > app > fieldProcessing.

Definitions for the Field Processing framework.

## readLoaderClassNameList

*class-string<Espo\Core\FieldProcessing\Loader\>[]*

A list of loader classes. Load additional fields when reading a record. Applied globally for all entity types.

## listLoaderClassNameList

*class-string<Espo\Core\FieldProcessing\Loader\>[]*

A list of loader classes for lists. Load additional fields when reading a list of record. Applied globally for all entity types.

## saverClassNameList

*class-string<Espo\Core\FieldProcessing\Saver\>[]*

A list of saver classes. Save additional fields. Applied globally for all entity types.
