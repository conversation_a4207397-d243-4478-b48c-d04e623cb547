# app > linkManager

Path: metadata > app > linkManager.

## createHookClassNameList

*class-string<Espo\Tools\LinkManager\Hook\CreateHook\>*

Hooks called when creating a new relationship (in the Entity Manager tool). Use `__APPEND__` for extending.

## deleteHookClassNameList

*class-string<Espo\Tools\LinkManager\Hook\DeleteHook\>*

Hooks called when deleting a relationship (in the Entity Manager tool). Use `__APPEND__` for extending.
