<?php
return [
  'useCache' => false,
  'jobMaxPortion' => 15,
  'jobRunInParallel' => true,
  'jobPoolConcurrencyNumber' => 8,
  'daemonMaxProcessNumber' => 5,
  'daemonInterval' => 10,
  'daemonProcessTimeout' => 36000,
  'recordsPerPage' => 20,
  'recordsPerPageSmall' => 5,
  'recordsPerPageSelect' => 10,
  'applicationName' => 'dev CRM',
  'version' => '9.1.7',
  'timeZone' => 'America/Santiago',
  'dateFormat' => 'DD/MM/YYYY',
  'timeFormat' => 'HH:mm',
  'weekStart' => 1,
  'thousandSeparator' => '.',
  'decimalMark' => ',',
  'exportDelimiter' => ',',
  'currencyList' => [
    0 => 'CLP'
  ],
  'defaultCurrency' => 'CLP',
  'baseCurrency' => 'CLP',
  'currencyRates' => [],
  'currencyNoJoinMode' => false,
  'outboundEmailIsShared' => true,
  'outboundEmailFromName' => 'dev',
  'outboundEmailFromAddress' => '',
  'smtpServer' => '',
  'smtpPort' => 587,
  'smtpAuth' => true,
  'smtpSecurity' => 'TLS',
  'smtpUsername' => '',
  'language' => 'en_US',
  'authenticationMethod' => 'Espo',
  'globalSearchEntityList' => [
    0 => 'Account',
    1 => 'Contact',
    2 => 'Lead',
    3 => 'Opportunity',
    4 => 'VdCall'
  ],
  'tabList' => [
    0 => 'Sms',
    1 => 'SmsList',
    2 => 'Interaction',
    3 => 'Debt',
    4 => 'Debtor',
    5 => 'Creditor',
    6 => 'Import',
    7 => 'Account',
    8 => 'Contact',
    9 => 'Lead',
    10 => 'Opportunity',
    11 => 'Case',
    12 => 'Email',
    13 => 'Calendar',
    14 => 'Meeting',
    15 => 'Call',
    16 => 'Task',
    17 => 'Document',
    18 => 'Campaign',
    19 => 'KnowledgeBaseArticle',
    20 => 'Stream',
    21 => 'User',
    22 => 'Report',
    23 => '_delimiter_',
    24 => 'VdCall',
    25 => 'TargetList',
    26 => (object) [
      'type' => 'divider',
      'text' => NULL,
      'id' => '317724'
    ],
    27 => (object) [
      'type' => 'group',
      'text' => '$SalesPack',
      'iconClass' => 'fas fa-boxes',
      'color' => NULL,
      'id' => '555993',
      'itemList' => [
        0 => 'Product',
        1 => (object) [
          'type' => 'divider',
          'text' => NULL,
          'id' => '538821'
        ],
        2 => 'Quote',
        3 => 'SalesOrder',
        4 => 'Invoice',
        5 => 'DeliveryOrder',
        6 => 'ReturnOrder',
        7 => (object) [
          'type' => 'divider',
          'text' => NULL,
          'id' => '473769'
        ],
        8 => 'PurchaseOrder',
        9 => 'ReceiptOrder',
        10 => (object) [
          'type' => 'divider',
          'text' => NULL,
          'id' => '508221'
        ],
        11 => 'TransferOrder',
        12 => 'InventoryAdjustment',
        13 => (object) [
          'type' => 'divider',
          'text' => NULL,
          'id' => '688906'
        ],
        14 => 'Warehouse',
        15 => 'InventoryNumber',
        16 => (object) [
          'type' => 'divider',
          'text' => NULL,
          'id' => '920618'
        ],
        17 => 'InventoryTransaction'
      ]
    ]
  ],
  'quickCreateList' => [
    0 => 'Sms',
    1 => 'Interaction',
    2 => 'Debt',
    3 => 'Debtor',
    4 => 'Creditor',
    5 => 'Import',
    6 => 'Account',
    7 => 'Contact',
    8 => 'Lead',
    9 => 'Opportunity',
    10 => 'Case',
    11 => 'Email',
    12 => 'Calendar',
    13 => 'Meeting',
    14 => 'Call',
    15 => 'Task',
    16 => 'Document',
    17 => 'Campaign',
    18 => 'KnowledgeBaseArticle',
    19 => 'Stream',
    20 => 'User',
    21 => 'Report',
    22 => '_delimiter_',
    23 => 'VdCall',
    24 => 'VdToolbox'
  ],
  'exportDisabled' => false,
  'adminNotifications' => false,
  'adminNotificationsNewVersion' => true,
  'adminNotificationsCronIsNotConfigured' => true,
  'adminNotificationsNewExtensionVersion' => true,
  'assignmentEmailNotifications' => false,
  'assignmentEmailNotificationsEntityList' => [
    0 => 'Lead',
    1 => 'Opportunity',
    2 => 'Task',
    3 => 'Case'
  ],
  'assignmentNotificationsEntityList' => [
    0 => 'Call',
    1 => 'Email',
    2 => 'BpmnUserTask'
  ],
  'portalStreamEmailNotifications' => true,
  'streamEmailNotificationsEntityList' => [
    0 => 'Case'
  ],
  'streamEmailNotificationsTypeList' => [
    0 => 'Post',
    1 => 'Status',
    2 => 'EmailReceived'
  ],
  'emailNotificationsDelay' => 30,
  'emailMessageMaxSize' => 10,
  'notificationsCheckInterval' => 10,
  'popupNotificationsCheckInterval' => 15,
  'maxEmailAccountCount' => 2,
  'followCreatedEntities' => false,
  'b2cMode' => false,
  'theme' => 'Light',
  'themeParams' => (object) [
    'navbar' => 'top'
  ],
  'massEmailMaxPerHourCount' => 100,
  'massEmailVerp' => false,
  'personalEmailMaxPortionSize' => 50,
  'inboundEmailMaxPortionSize' => 50,
  'emailAddressLookupEntityTypeList' => [
    0 => 'User',
    1 => 'Contact',
    2 => 'Lead',
    3 => 'Account'
  ],
  'authTokenLifetime' => 0,
  'authTokenMaxIdleTime' => 48,
  'userNameRegularExpression' => '[^a-z0-9\\-@_\\.\\s]',
  'addressFormat' => 1,
  'displayListViewRecordCount' => true,
  'dashboardLayout' => [
    0 => (object) [
      'name' => 'My Espo',
      'layout' => [
        0 => (object) [
          'id' => 'default-stream',
          'name' => 'Stream',
          'x' => 0,
          'y' => 0,
          'width' => 2,
          'height' => 4
        ],
        1 => (object) [
          'id' => 'default-activities',
          'name' => 'Activities',
          'x' => 2,
          'y' => 2,
          'width' => 2,
          'height' => 4
        ]
      ]
    ]
  ],
  'calendarEntityList' => [
    0 => 'Meeting',
    1 => 'Call',
    2 => 'Task'
  ],
  'activitiesEntityList' => [
    0 => 'Meeting',
    1 => 'Call'
  ],
  'historyEntityList' => [
    0 => 'Meeting',
    1 => 'Call',
    2 => 'Email',
    3 => 'VdCall'
  ],
  'busyRangesEntityList' => [
    0 => 'Meeting',
    1 => 'Call',
    2 => 'VdCall'
  ],
  'emailAutoReplySuppressPeriod' => '2 hours',
  'emailAutoReplyLimit' => 5,
  'cleanupJobPeriod' => '1 month',
  'cleanupActionHistoryPeriod' => '15 days',
  'cleanupAuthTokenPeriod' => '1 month',
  'cleanupSubscribers' => true,
  'currencyFormat' => 2,
  'currencyDecimalPlaces' => 2,
  'aclAllowDeleteCreated' => false,
  'aclAllowDeleteCreatedThresholdPeriod' => '24 hours',
  'attachmentUploadMaxSize' => 256,
  'attachmentUploadChunkSize' => 4,
  'inlineAttachmentUploadMaxSize' => 20,
  'textFilterUseContainsForVarchar' => false,
  'tabColorsDisabled' => false,
  'massPrintPdfMaxCount' => 50,
  'emailKeepParentTeamsEntityList' => [
    0 => 'Case'
  ],
  'streamEmailWithContentEntityTypeList' => [
    0 => 'Case'
  ],
  'recordListMaxSizeLimit' => 200,
  'noteDeleteThresholdPeriod' => '1 month',
  'noteEditThresholdPeriod' => '7 days',
  'emailForceUseExternalClient' => false,
  'useWebSocket' => true,
  'auth2FAMethodList' => [
    0 => 'Totp'
  ],
  'personNameFormat' => 'firstLast',
  'newNotificationCountInTitle' => false,
  'pdfEngine' => 'Dompdf',
  'smsProvider' => 'AlarisSms',
  'defaultFileStorage' => 'EspoUploadDir',
  'ldapUserNameAttribute' => 'sAMAccountName',
  'ldapUserFirstNameAttribute' => 'givenName',
  'ldapUserLastNameAttribute' => 'sn',
  'ldapUserTitleAttribute' => 'title',
  'ldapUserEmailAddressAttribute' => 'mail',
  'ldapUserPhoneNumberAttribute' => 'telephoneNumber',
  'ldapUserObjectClass' => 'person',
  'ldapPortalUserLdapAuth' => false,
  'passwordGenerateLength' => 10,
  'massActionIdleCountThreshold' => 100,
  'exportIdleCountThreshold' => 1000,
  'oidcJwtSignatureAlgorithmList' => [
    0 => 'RS256'
  ],
  'oidcUsernameClaim' => 'sub',
  'oidcFallback' => true,
  'oidcScopes' => [
    0 => 'profile',
    1 => 'email',
    2 => 'phone'
  ],
  'cacheTimestamp' => **********,
  'microtime' => **********.826923,
  'siteUrl' => 'https://crm.dev.simtastic.cl',
  'fullTextSearchMinLength' => 4,
  'appTimestamp' => **********,
  'adminPanelIframeDisabled' => true,
  'userThemesDisabled' => true,
  'workingTimeCalendarName' => NULL,
  'workingTimeCalendarId' => NULL,
  'maintenanceMode' => false,
  'cronDisabled' => false,
  'emailAddressIsOptedOutByDefault' => false,
  'cleanupDeletedRecords' => false,
  'fiscalYearShift' => 0,
  'addressCityList' => [],
  'addressStateList' => [],
  'addressCountryList' => [],
  'avatarsDisabled' => false,
  'scopeColorsDisabled' => false,
  'tabIconsDisabled' => false,
  'dashletsOptions' => (object) [],
  'companyLogoId' => '65008b9412b9017f0',
  'companyLogoName' => 'simtastic_logo4b.png',
  'adminPanelIframeUrl' => 'https://s.espocrm.com/?sales-pack=1db362fcdb6b077a526eb8b1c9b6934a',
  'webSocketUrl' => 'wss://crm.dev.simtastic.cl:8082',
  'outboundSmsFromNumber' => '*********',
  'integrations' => (object) [
    'Smsimple' => true,
    'Bsale' => true,
    'CaudalesSms' => true,
    'Sms' => true,
    'SiptelSms' => true,
    'AlarisSms' => true
  ],
  'temporaryUpgradeParams6491cd8d8c7157b95' => [
    'maintenanceMode' => false,
    'cronDisabled' => false,
    'useCache' => true
  ],
  'recordsPerPageKanban' => 5,
  'emailAddressEntityLookupDefaultOrder' => [
    0 => 'User',
    1 => 'Contact',
    2 => 'Lead',
    3 => 'Account'
  ],
  'phoneNumberEntityLookupDefaultOrder' => [
    0 => 'User',
    1 => 'Contact',
    2 => 'Lead',
    3 => 'Account'
  ],
  'auth2FAInPortal' => false,
  'emailRecipientAddressMaxCount' => 100,
  'attachmentAvailableStorageList' => NULL,
  'massEmailMaxPerBatchCount' => NULL,
  'mapProvider' => 'Google',
  'listViewSettingsDisabled' => false,
  'phoneNumberNumericSearch' => false,
  'phoneNumberInternational' => false,
  'phoneNumberPreferredCountryList' => [
    0 => 'us',
    1 => 'de'
  ],
  'jobForceUtc' => true,
  'emailAddressSelectEntityTypeList' => [
    0 => 'User',
    1 => 'Contact',
    2 => 'Lead',
    3 => 'Account'
  ],
  'cleanupAudit' => true,
  'cleanupAuditPeriod' => '3 months',
  'wysiwygCodeEditorDisabled' => false,
  'customPrefixDisabled' => true,
  'listPagination' => true,
  'appLogAdminAllowed' => false,
  'notePinnedMaxCount' => 5,
  'oidcAuthorizationPrompt' => 'consent',
  'phoneNumberExtensions' => false,
  'starsLimit' => 500,
  'quickSearchFullTextAppendWildcard' => false,
  'authIpAddressCheck' => false,
  'authIpAddressWhitelist' => [],
  'authIpAddressCheckExcludedUsersIds' => [],
  'authIpAddressCheckExcludedUsersNames' => (object) [],
  'advancedPackUpdateSkipWorkflowOrder' => true,
  'tabQuickSearch' => true,
  'passwordStrengthLength' => NULL,
  'passwordStrengthLetterCount' => NULL,
  'passwordStrengthNumberCount' => NULL,
  'passwordStrengthBothCases' => false,
  'passwordStrengthSpecialCharacterCount' => NULL,
  'availableReactions' => [
    0 => 'Like'
  ],
  'streamReactionsCheckMaxSize' => 50,
  'emailScheduledBatchCount' => 50,
  'emailAddressMaxCount' => 10,
  'phoneNumberMaxCount' => 10,
  'iframeSandboxExcludeDomainList' => [
    0 => 'youtube.com',
    1 => 'google.com'
  ]
];
