/************************************************************************
 * This file is part of EspoCRM.
 *
 * EspoCRM – Open Source CRM application.
 * Copyright (C) 2014-2024 <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>
 * Website: https://www.espocrm.com
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 * The interactive user interfaces in modified source and object code versions
 * of this program must display Appropriate Legal Notices, as required under
 * Section 5 of the GNU Affero General Public License version 3.
 *
 * In accordance with Section 7(b) of the GNU Affero General Public License version 3,
 * these Appropriate Legal Notices must retain the display of the "EspoCRM" word.
 ************************************************************************/

import TextFieldView from 'views/fields/text';

/**
 * A hideable text field that can toggle between visible and hidden (password-like) display.
 */
class TextHideableFieldView extends TextFieldView {

    type = 'textHideable'

    detailTemplate = 'custom:fields/text-hideable/detail'
    editTemplate = 'custom:fields/text-hideable/edit'
    listTemplate = 'custom:fields/text-hideable/list'
    searchTemplate = 'custom:fields/text-hideable/search'

    events = {
        ...TextFieldView.prototype.events,
        /** @this TextHideableFieldView */
        'click [data-action="toggleVisibility"]': function () {
            this.toggleVisibility();
        },
    }

    setup() {
        super.setup();
        
        // Initialize visibility state
        this.isHidden = this.params.hideByDefault !== false;
        this.showToggleButton = this.params.showToggleButton !== false;
    }

    /**
     * Toggle the visibility of the text content
     */
    toggleVisibility() {
        this.isHidden = !this.isHidden;
        
        if (this.isReadMode()) {
            this.reRender();
        } else if (this.mode === this.MODE_EDIT) {
            this.updateInputType();
        }
    }

    /**
     * Update the input type for edit mode
     */
    updateInputType() {
        if (this.$element && this.$element.length) {
            // For textarea, we'll handle visibility through CSS or content masking
            this.updateTextareaDisplay();
        }
    }

    /**
     * Update textarea display based on visibility state
     */
    updateTextareaDisplay() {
        if (!this.$element) return;
        
        if (this.isHidden) {
            this.$element.addClass('text-hideable-hidden');
        } else {
            this.$element.removeClass('text-hideable-hidden');
        }
    }

    data() {
        const data = super.data();
        
        data.isHidden = this.isHidden;
        data.showToggleButton = this.showToggleButton;
        data.toggleButtonText = this.isHidden ? 
            this.translate('show', 'labels', 'Global') : 
            this.translate('hide', 'labels', 'Global');
        data.toggleButtonIcon = this.isHidden ? 'fa-eye' : 'fa-eye-slash';
        
        // For detail mode, mask the content if hidden
        if (this.isReadMode() && this.isHidden && data.isNotEmpty) {
            data.maskedValue = this.getMaskedValue();
        }
        
        return data;
    }

    /**
     * Get masked version of the value for display when hidden
     */
    getMaskedValue() {
        const value = this.model.get(this.name);
        if (!value) return '';
        
        // Create a masked version - show first few characters and mask the rest
        const visibleChars = Math.min(3, value.length);
        const maskedPart = '*'.repeat(Math.max(0, value.length - visibleChars));
        
        return value.substring(0, visibleChars) + maskedPart;
    }

    afterRender() {
        super.afterRender();
        
        if (this.mode === this.MODE_EDIT) {
            this.updateTextareaDisplay();
        }
    }

    getValueForDisplay() {
        if (this.isReadMode() && this.isHidden) {
            return this.getMaskedValue();
        }
        
        return super.getValueForDisplay();
    }
}

export default TextHideableFieldView;
