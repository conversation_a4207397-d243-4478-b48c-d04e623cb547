/************************************************************************
 * This file is part of EspoCRM.
 *
 * EspoCRM – Open Source CRM application.
 * Copyright (C) 2014-2024 <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>
 * Website: https://www.espocrm.com
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 * The interactive user interfaces in modified source and object code versions
 * of this program must display Appropriate Legal Notices, as required under
 * Section 5 of the GNU Affero General Public License version 3.
 *
 * In accordance with Section 7(b) of the GNU Affero General Public License version 3,
 * these Appropriate Legal Notices must retain the display of the "EspoCRM" word.
 ************************************************************************/

define('text-hideable:views/fields/text-hideable', ['views/fields/varchar'], function (VarcharFieldView) {

    /**
     * A hideable varchar field that can toggle between visible and hidden (password-like) display.
     */
    return class extends VarcharFieldView {

        type = 'textHideable'

        detailTemplate = 'text-hideable:fields/text-hideable/detail'
        editTemplate = 'text-hideable:fields/text-hideable/edit'
        listTemplate = 'text-hideable:fields/text-hideable/list'
        searchTemplate = 'text-hideable:fields/text-hideable/search'

        events = {
            ...VarcharFieldView.prototype.events,
            /** @this TextHideableFieldView */
            'click .text-hideable-toggle-link': function () {
                this.toggleVisibility();
            },
            /** @this TextHideableFieldView */
            'click [data-action="toggleVisibility"]': function () {
                this.toggleVisibility();
            },
        }

    setup() {
        super.setup();

        // Initialize visibility state
        this.isHidden = this.params.hideByDefault !== false;
        this.showToggleButton = this.params.showToggleButton !== false;
        this.dynamicButtonAdded = false; // Track if dynamic button was added
    }

    /**
     * Toggle the visibility of the text content
     */
    toggleVisibility() {
        this.isHidden = !this.isHidden;

        if (this.mode === this.MODE_DETAIL) {
            this.updateToggleButtonIcon();
            this.updateContentDisplay();
        } else if (this.mode === this.MODE_EDIT) {
            this.updateInputDisplay();
            this.updateTemplateButtonIcon();
        } else if (this.mode === this.MODE_LIST || this.mode === this.MODE_LIST_LINK) {
            this.updateListViewContent();
        }
    }

    /**
     * Update the toggle button icon (only for detail mode dynamic button)
     */
    updateToggleButtonIcon() {
        if (this.mode !== this.MODE_DETAIL) {
            return;
        }

        const $cell = this.findFieldCell();
        const $icon = $cell.find('.text-hideable-toggle-link span');

        if ($icon.length) {
            $icon.removeClass('fa-eye fa-eye-slash')
                 .addClass(this.isHidden ? 'fa-eye' : 'fa-eye-slash');
        }
    }

    /**
     * Update the content display in detail mode without re-rendering
     */
    updateContentDisplay() {
        const $complexText = this.$el.find('.complex-text');
        if (!$complexText.length) return;

        if (this.isHidden) {
            const maskedValue = this.getMaskedValue();
            const $maskedSpan = $(`<span class="text-hideable-masked">${maskedValue}</span>`);

            // Apply the correct color directly via JavaScript
            $maskedSpan.css({
                'color': 'var(--text-gray-color)',
                'letter-spacing': '1px',
                'font-size': '0.9em'
            });

            $complexText.empty().append($maskedSpan);
        } else {
            const value = this.model.get(this.name);
            if (value) {
                const escapedValue = $('<div>').text(value).html();
                $complexText.html(escapedValue);
            } else {
                $complexText.html(`<span class="none-value">${this.translate('None')}</span>`);
            }
        }
    }

    /**
     * Update the template button icon in edit mode
     */
    updateTemplateButtonIcon() {
        const $icon = this.$el.find('.text-hideable-toggle-btn span');
        if ($icon.length) {
            $icon.removeClass('fa-eye fa-eye-slash')
                 .addClass(this.isHidden ? 'fa-eye' : 'fa-eye-slash');
        }
    }

    /**
     * Update the input display for edit mode
     */
    updateInputDisplay() {
        if (this.$element?.length) {
            this.$element.attr('type', this.isHidden ? 'password' : 'text');
        }
    }

    /**
     * Update the content display in list views
     */
    updateListViewContent() {
        if (this.isHidden) {
            const maskedValue = this.getMaskedValue();
            const $maskedSpan = $(`<span class="text-hideable-masked" style="color: var(--text-gray-color); letter-spacing: 1px; font-size: 0.9em;">${maskedValue}</span>`);
            this.$el.empty().append($maskedSpan);
        } else {
            const value = this.model.get(this.name);
            if (value) {
                const escapedValue = $('<div>').text(value).html();
                this.$el.html(escapedValue);
            } else {
                this.$el.html(`<span class="none-value">${this.translate('None')}</span>`);
            }
        }
    }

    data() {
        const data = super.data();

        data.isHidden = this.isHidden;
        data.showToggleButton = this.showToggleButton;
        data.toggleButtonIcon = this.isHidden ? 'fa-eye' : 'fa-eye-slash';

        if (this.isReadMode() && this.isHidden && data.isNotEmpty) {
            data.maskedValue = this.getMaskedValue();
        }

        return data;
    }

    /**
     * Get masked version of the value for display when hidden
     */
    getMaskedValue() {
        return this.model.get(this.name) ? '●●●●●●●' : '';
    }

    afterRender() {
        super.afterRender();

        if (this.mode === this.MODE_EDIT) {
            this.updateInputDisplay();
            this.removeDynamicButton();
            if (this.showToggleButton) {
                this.updateTemplateButtonIcon();
            }
        } else if (this.mode === this.MODE_DETAIL && this.showToggleButton) {
            // Use setTimeout to avoid flicker when transitioning from edit mode
            setTimeout(() => {
                if (this.mode === this.MODE_DETAIL && !this.dynamicButtonAdded) {
                    this.dynamicButtonAdded = true;

                    // Use different initialization for detail-small views
                    if (this.isDetailSmallView()) {
                        this.initToggleButtonForSmallView();
                    } else {
                        this.initToggleButtonSimple();
                        this.monitorForEditButton();
                    }

                    // Check if mouse is already hovering over the field
                    this.checkInitialHoverState();
                }
            }, 10);
        } else if ((this.mode === this.MODE_LIST || this.mode === this.MODE_LIST_LINK) && this.showToggleButton) {
            // Enable click-to-toggle in list views
            this.initListViewToggle();
        }
    }

    /**
     * Monitor for the edit button and reorder when it appears
     */
    monitorForEditButton() {
        let attempts = 0;
        const maxAttempts = 20;

        const checkAndReorder = () => {
            attempts++;
            const $cell = this.findFieldCell();
            const $toggleLink = $cell.find('.text-hideable-toggle-link');
            const $editLink = $cell.find('.inline-edit-link');

            if ($toggleLink.length && $editLink.length) {
                $toggleLink.insertAfter($editLink);
                return;
            }

            if (attempts < maxAttempts) {
                setTimeout(checkAndReorder, 100);
            }
        };

        setTimeout(checkAndReorder, 50);
    }

    /**
     * Helper method to find the field cell container
     */
    findFieldCell() {
        let $cell = this.get$cell();

        if ($cell.length === 0) {
            $cell = this.$el.closest('.field-content');
        }
        if ($cell.length === 0) {
            $cell = this.$el.closest('.field').find('.field-content');
        }
        if ($cell.length === 0) {
            $cell = this.$el.closest('.field');
        }
        if ($cell.length === 0) {
            $cell = this.$el.parent();
        }

        return $cell;
    }

    /**
     * Check if we're in a detail-small view (popup)
     */
    isDetailSmallView() {
        // Check if we're inside a modal or detail-small container
        return this.$el.closest('.modal').length > 0 ||
               this.$el.closest('.detail-small').length > 0 ||
               this.$el.closest('[data-name="detailSmall"]').length > 0;
    }

    /**
     * Check if mouse is already hovering and show button if needed
     */
    checkInitialHoverState() {
        const $cell = this.findFieldCell();
        const $toggleButton = $cell.find('.text-hideable-toggle-link');

        if ($toggleButton.length && $cell.is(':hover')) {
            // Mouse is already over the field, show the button
            setTimeout(() => {
                $toggleButton.removeClass('hidden');
            }, 50);
        }
    }

    /**
     * Initialize click-to-toggle functionality for list views
     */
    initListViewToggle() {
        // Make the field content clickable
        this.$el.css({
            'cursor': 'pointer',
            'user-select': 'none'
        });

        // Add click handler to the field content
        this.$el.on('click.text-hideable', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.toggleVisibility();
        });

        // Add visual feedback on hover
        this.$el.on('mouseenter.text-hideable', () => {
            this.$el.css('opacity', '0.8');
        });

        this.$el.on('mouseleave.text-hideable', () => {
            this.$el.css('opacity', '1');
        });
    }

    /**
     * Remove dynamic toggle button and list view handlers
     */
    removeDynamicButton() {
        const $cell = this.findFieldCell();
        const $dynamicButton = $cell.find('.text-hideable-toggle-link');

        if ($dynamicButton.length) {
            $dynamicButton.remove();
            $cell.off('mouseenter.text-hideable mouseleave.text-hideable');
        }

        // Remove list view click handlers
        this.$el.off('click.text-hideable mouseenter.text-hideable mouseleave.text-hideable');
        this.$el.css({
            'cursor': '',
            'user-select': '',
            'opacity': ''
        });

        this.dynamicButtonAdded = false;
    }

    /**
     * Override to prevent flicker during inline edit transitions
     */
    inlineEditClose() {
        // Store the current state to restore after re-render
        const wasHidden = this.isHidden;

        // Call parent method
        super.inlineEditClose();

        // Restore state after re-render
        this.isHidden = wasHidden;
    }

    /**
     * Initialize toggle button for detail-small views (popups)
     */
    initToggleButtonForSmallView() {
        // Try multiple ways to find the label in detail-small view
        let $label = null;
        let $cell = null;

        // Method 1: Look for the label in the same row
        $cell = this.$el.closest('tr, .row, .cell');
        if ($cell.length) {
            $label = $cell.find('.field-label, label').first();
        }

        // Method 2: Look for label by field name
        if (!$label || $label.length === 0) {
            $label = $(`label[data-name="${this.name}"], .field-label[data-name="${this.name}"]`);
        }

        // Method 3: Look for any label containing our field name
        if (!$label || $label.length === 0) {
            $label = $('label, .field-label').filter((_, el) => {
                return $(el).text().trim() === this.getLabelText();
            });
        }

        if (!$label || $label.length === 0) {
            // Fallback to normal behavior if label not found
            return this.initToggleButtonSimple();
        }

        // Remove existing button
        $label.find('.text-hideable-toggle-link').remove();

        // Create toggle button positioned next to the label
        const $toggleLink = $('<a>')
            .attr('role', 'button')
            .addClass('text-hideable-toggle-link hidden')
            .attr('title', this.translate('Toggle Visibility'))
            .css({
                'cursor': 'pointer',
                'margin-left': '8px',
                'color': '#888',
                'text-decoration': 'none',
                'display': 'inline'
            })
            .append($('<span>').addClass(`fas ${this.isHidden ? 'fa-eye' : 'fa-eye-slash'} fa-sm`));

        // Append button to the label
        $label.append($toggleLink);

        // Add click handler
        $toggleLink.on('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.toggleVisibility();
        });

        // Add hover handlers for button color
        $toggleLink
            .on('mouseenter', (e) => {
                $(e.currentTarget).css('color', '#555');
            })
            .on('mouseleave', (e) => {
                $(e.currentTarget).css('color', '#888');
            });

        // Add hover handlers for visibility on the entire row/cell
        if (!$cell || $cell.length === 0) {
            $cell = $label.closest('tr, .row, .cell');
        }

        $cell
            .off('mouseenter.text-hideable mouseleave.text-hideable')
            .on('mouseenter.text-hideable', (e) => {
                e.stopPropagation();
                $toggleLink.removeClass('hidden');
            })
            .on('mouseleave.text-hideable', (e) => {
                e.stopPropagation();
                $toggleLink.addClass('hidden');
            });
    }

    /**
     * Initialize toggle button for detail mode
     */
    initToggleButtonSimple() {
        if (this.mode !== this.MODE_DETAIL) {
            return;
        }

        const $cell = this.findFieldCell();
        if ($cell.length === 0) {
            return;
        }

        // Remove existing button
        $cell.find('.text-hideable-toggle-link').remove();

        // Create toggle button
        const $toggleLink = $('<a>')
            .attr('role', 'button')
            .addClass('text-hideable-toggle-link hidden')
            .attr('title', this.translate('Toggle Visibility'))
            .css({
                'cursor': 'pointer',
                'float': 'right',
                'margin-right': '8px',
                'color': '#888',
                'text-decoration': 'none'
            })
            .append($('<span>').addClass(`fas ${this.isHidden ? 'fa-eye' : 'fa-eye-slash'} fa-sm`));

        $cell.append($toggleLink);

        // Add click handler
        $toggleLink.on('click', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.toggleVisibility();
        });

        // Add hover handlers for button color
        $toggleLink
            .on('mouseenter', (e) => {
                $(e.currentTarget).css('color', '#555');
            })
            .on('mouseleave', (e) => {
                $(e.currentTarget).css('color', '#888');
            });

        // Add hover handlers for visibility
        $cell
            .off('mouseenter.text-hideable mouseleave.text-hideable')
            .on('mouseenter.text-hideable', (e) => {
                e.stopPropagation();
                $toggleLink.removeClass('hidden');
            })
            .on('mouseleave.text-hideable', (e) => {
                e.stopPropagation();
                $toggleLink.addClass('hidden');
            });
    }
    };
});
