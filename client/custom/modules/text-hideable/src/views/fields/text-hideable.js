/************************************************************************
 * This file is part of EspoCRM.
 *
 * EspoCRM – Open Source CRM application.
 * Copyright (C) 2014-2024 <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>
 * Website: https://www.espocrm.com
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 * The interactive user interfaces in modified source and object code versions
 * of this program must display Appropriate Legal Notices, as required under
 * Section 5 of the GNU Affero General Public License version 3.
 *
 * In accordance with Section 7(b) of the GNU Affero General Public License version 3,
 * these Appropriate Legal Notices must retain the display of the "EspoCRM" word.
 ************************************************************************/

define('text-hideable:views/fields/text-hideable', ['views/fields/varchar'], function (VarcharFieldView) {

    /**
     * A hideable varchar field that can toggle between visible and hidden (password-like) display.
     */
    return class extends VarcharFieldView {

        type = 'textHideable'

        detailTemplate = 'text-hideable:fields/text-hideable/detail'
        editTemplate = 'text-hideable:fields/text-hideable/edit'
        listTemplate = 'text-hideable:fields/text-hideable/list'
        searchTemplate = 'text-hideable:fields/text-hideable/search'

        events = {
            ...VarcharFieldView.prototype.events,
            /** @this TextHideableFieldView */
            'click .text-hideable-toggle-link': function () {
                this.toggleVisibility();
            },
            /** @this TextHideableFieldView */
            'click [data-action="toggleVisibility"]': function () {
                this.toggleVisibility();
            },
        }

    setup() {
        super.setup();

        // Initialize visibility state
        this.isHidden = this.params.hideByDefault !== false;
        this.showToggleButton = this.params.showToggleButton !== false;
        this.dynamicButtonAdded = false; // Track if dynamic button was added
    }

    /**
     * Toggle the visibility of the text content
     */
    toggleVisibility() {
        console.log('TextHideable: toggleVisibility called, current state:', this.isHidden);

        this.isHidden = !this.isHidden;

        console.log('TextHideable: new state:', this.isHidden);

        if (this.mode === this.MODE_DETAIL) {
            // Update the dynamic toggle button icon
            this.updateToggleButtonIcon();
            // Update content directly without re-rendering to avoid flicker
            this.updateContentDisplay();
        } else if (this.mode === this.MODE_EDIT) {
            // Update input type and template button icon
            this.updateInputDisplay();
            this.updateTemplateButtonIcon();
        }
    }

    /**
     * Update the toggle button icon (only for detail mode dynamic button)
     */
    updateToggleButtonIcon() {
        // Only update dynamic button in detail mode (not in any edit mode)
        if (this.mode !== this.MODE_DETAIL) {
            return;
        }

        let $cell = this.get$cell();

        if ($cell.length === 0) {
            $cell = this.$el.closest('.field-content');
        }
        if ($cell.length === 0) {
            $cell = this.$el.closest('.field').find('.field-content');
        }
        if ($cell.length === 0) {
            $cell = this.$el.parent();
        }

        if ($cell.length > 0) {
            const $toggleLink = $cell.find('.text-hideable-toggle-link');
            const $icon = $toggleLink.find('span');

            if ($icon.length) {
                $icon.removeClass('fa-eye fa-eye-slash');
                $icon.addClass(this.isHidden ? 'fa-eye' : 'fa-eye-slash');
                console.log('TextHideable: Icon updated to:', this.isHidden ? 'fa-eye' : 'fa-eye-slash');
            }
        }
    }

    /**
     * Update the content display in detail mode without re-rendering
     */
    updateContentDisplay() {
        const $complexText = this.$el.find('.complex-text');

        if ($complexText.length) {
            if (this.isHidden) {
                // Show masked content
                const maskedValue = this.getMaskedValue();
                $complexText.html(`<span style="color: #666; letter-spacing: 1px; font-size: 0.9em;">${maskedValue}</span>`);
            } else {
                // Show original content
                const value = this.model.get(this.name);
                if (value) {
                    // For varchar fields, just display the text directly
                    const escapedValue = $('<div>').text(value).html(); // Escape HTML
                    $complexText.html(escapedValue);
                } else {
                    $complexText.html('<span class="none-value">' + this.translate('None') + '</span>');
                }
            }
        }
    }

    /**
     * Update the template button icon in edit mode
     */
    updateTemplateButtonIcon() {
        const $templateButton = this.$el.find('.text-hideable-toggle-btn span');
        if ($templateButton.length) {
            $templateButton
                .removeClass('fa-eye fa-eye-slash')
                .addClass(this.isHidden ? 'fa-eye' : 'fa-eye-slash');
        }
    }

    /**
     * Update the input display for edit mode
     */
    updateInputDisplay() {
        if (this.$element && this.$element.length) {
            // For varchar input, change the type attribute
            if (this.isHidden) {
                this.$element.attr('type', 'password');
            } else {
                this.$element.attr('type', 'text');
            }
        }
    }

    data() {
        const data = super.data();
        
        data.isHidden = this.isHidden;
        data.showToggleButton = this.showToggleButton;
        data.toggleButtonText = this.isHidden ? 
            this.translate('show', 'labels', 'Global') : 
            this.translate('hide', 'labels', 'Global');
        data.toggleButtonIcon = this.isHidden ? 'fa-eye' : 'fa-eye-slash';
        
        // For detail mode, mask the content if hidden
        if (this.isReadMode() && this.isHidden && data.isNotEmpty) {
            data.maskedValue = this.getMaskedValue();
        }
        
        return data;
    }

    /**
     * Check if we're in edit mode
     */
    isEditMode() {
        return this.mode === this.MODE_EDIT || this.mode === 'edit';
    }

    /**
     * Get masked version of the value for display when hidden
     */
    getMaskedValue() {
        const value = this.model.get(this.name);
        if (!value) return '';

        // Return a fixed number of circles with reduced spacing
        // Don't reveal any information about the actual content length
        return '●●●●●●●';
    }

    afterRender() {
        super.afterRender();

        if (this.mode === this.MODE_EDIT) {
            this.updateInputDisplay();
        }

        console.log('TextHideable: afterRender - mode:', this.mode, 'dynamicButtonAdded:', this.dynamicButtonAdded);

        if (this.mode === this.MODE_DETAIL && this.showToggleButton && !this.dynamicButtonAdded) {
            // Add dynamic toggle button in detail mode
            console.log('TextHideable: Adding dynamic button in detail mode');
            this.dynamicButtonAdded = true;
            this.initToggleButtonSimple();
            this.monitorForEditButton();
        } else if (this.mode === this.MODE_EDIT) {
            // Remove dynamic button in edit mode (if it exists)
            console.log('TextHideable: Removing dynamic button in edit mode');
            this.removeDynamicButton();

            // Update template button icon
            if (this.showToggleButton) {
                console.log('TextHideable: Updating template button in edit mode');
                this.updateTemplateButtonIcon();
            }
        } else {
            console.log('TextHideable: No action needed - mode:', this.mode, 'already added:', this.dynamicButtonAdded);
        }
    }

    /**
     * Monitor for the edit button and reorder when it appears
     */
    monitorForEditButton() {
        let attempts = 0;
        const maxAttempts = 20;

        const checkAndReorder = () => {
            attempts++;
            const $cell = this.get$cell().length ? this.get$cell() : this.$el.parent();
            const $toggleLink = $cell.find('.text-hideable-toggle-link');
            const $editLink = $cell.find('.inline-edit-link');

            if ($toggleLink.length && $editLink.length) {
                // With float:right, the last element appears first (leftmost)
                // So we need to insert toggle AFTER edit to make toggle appear first visually
                $toggleLink.insertAfter($editLink);
                console.log('TextHideable: Buttons reordered - toggle first, edit second');
                return; // Stop monitoring
            }

            if (attempts < maxAttempts) {
                setTimeout(checkAndReorder, 100);
            }
        };

        setTimeout(checkAndReorder, 50);
    }

    /**
     * Initialize the toggle visibility button similar to inline edit button
     */
    initToggleButton() {
        console.log('TextHideable: initToggleButton called');

        // Try multiple ways to find the cell container
        let $cell = this.get$cell();
        console.log('TextHideable: get$cell() result:', $cell.length);

        // Fallback: try to find the field container
        if ($cell.length === 0) {
            $cell = this.$el.closest('.field');
            console.log('TextHideable: closest(.field) result:', $cell.length);
        }

        // Another fallback: try parent with field class
        if ($cell.length === 0) {
            $cell = this.$el.parent('.field');
            console.log('TextHideable: parent(.field) result:', $cell.length);
        }

        // Last fallback: use the element's parent
        if ($cell.length === 0) {
            $cell = this.$el.parent();
            console.log('TextHideable: parent() result:', $cell.length);
        }

        if ($cell.length === 0) {
            console.warn('TextHideable: Could not find field cell container');
            return;
        }

        console.log('TextHideable: Found cell container:', $cell[0]);

        // Remove existing toggle button if any
        $cell.find('.text-hideable-toggle-link').remove();

        const $toggleLink = $('<a>')
            .attr('role', 'button')
            .addClass('pull-right text-hideable-toggle-link hidden')
            .attr('title', this.translate('Toggle Visibility'))
            .append(
                $('<span>').addClass(`fas ${this.isHidden ? 'fa-eye' : 'fa-eye-slash'} fa-sm`)
            );

        console.log('TextHideable: Created toggle button:', $toggleLink[0]);

        // Add click handler directly to the button
        $toggleLink.on('click', (e) => {
            console.log('TextHideable: Button clicked!');
            e.preventDefault();
            e.stopPropagation();
            this.toggleVisibility();
        });

        // Insert before the inline edit button if it exists
        const $editLink = $cell.find('.inline-edit-link');
        if ($editLink.length) {
            $editLink.before($toggleLink);
        } else {
            $cell.prepend($toggleLink);
        }

        // Add hover handlers to the cell
        $cell
            .off('mouseenter.text-hideable mouseleave.text-hideable') // Remove existing handlers
            .on('mouseenter.text-hideable', (e) => {
                e.stopPropagation();
                if (this.isDetailMode()) {
                    $toggleLink.removeClass('hidden');
                }
            })
            .on('mouseleave.text-hideable', (e) => {
                e.stopPropagation();
                if (this.isDetailMode()) {
                    $toggleLink.addClass('hidden');
                }
            });
    }

    getValueForDisplay() {
        // Don't interfere with direct content updates
        return super.getValueForDisplay();
    }

    /**
     * Remove dynamic toggle button
     */
    removeDynamicButton() {
        console.log('TextHideable: removeDynamicButton called');

        // Find and remove any existing dynamic toggle buttons
        const $cell = this.get$cell().length ? this.get$cell() : this.$el.parent();
        const $dynamicButton = $cell.find('.text-hideable-toggle-link');

        if ($dynamicButton.length) {
            console.log('TextHideable: Removing', $dynamicButton.length, 'dynamic button(s)');
            $dynamicButton.remove();

            // Remove hover handlers
            $cell.off('mouseenter.text-hideable mouseleave.text-hideable');
        } else {
            console.log('TextHideable: No dynamic buttons found to remove');
        }
    }

    /**
     * Simplified toggle button initialization
     */
    initToggleButtonSimple() {
        console.log('TextHideable: initToggleButtonSimple called - mode:', this.mode);

        // Double check: don't add dynamic button in edit mode
        if (this.mode !== this.MODE_DETAIL) {
            console.log('TextHideable: Aborting initToggleButtonSimple - not in detail mode');
            return;
        }

        // Find the field cell (the container that holds the field content)
        let $cell = this.get$cell();

        // Fallback methods to find the cell for different modes
        if ($cell.length === 0) {
            $cell = this.$el.closest('.field-content');
        }
        if ($cell.length === 0) {
            $cell = this.$el.closest('.field').find('.field-content');
        }
        if ($cell.length === 0) {
            // In edit mode, the structure might be different
            $cell = this.$el.closest('.field');
        }
        if ($cell.length === 0) {
            $cell = this.$el.parent();
        }

        if ($cell.length === 0) {
            console.warn('TextHideable: Could not find field cell');
            return;
        }

        console.log('TextHideable: Found field cell:', $cell[0]);

        // Remove existing button
        $cell.find('.text-hideable-toggle-link').remove();

        // Create button with different styling for edit vs detail mode
        const isEditMode = this.isEditMode();
        const $toggleLink = $('<a>')
            .attr('role', 'button')
            .addClass(`text-hideable-toggle-link ${isEditMode ? '' : 'hidden'}`) // Always visible in edit mode
            .attr('title', this.translate('Toggle Visibility'))
            .css({
                'cursor': 'pointer',
                'z-index': '1',
                'position': isEditMode ? 'absolute' : 'relative',
                'right': isEditMode ? '10px' : 'auto',
                'top': isEditMode ? '50%' : 'auto',
                'transform': isEditMode ? 'translateY(-50%)' : 'none',
                'float': isEditMode ? 'none' : 'right',
                'margin-right': isEditMode ? '0' : '8px'
            })
            .append(
                $('<span>').addClass(`fas ${this.isHidden ? 'fa-eye' : 'fa-eye-slash'} fa-sm`)
            );

        // Remove existing button first
        $cell.find('.text-hideable-toggle-link').remove();

        // Position the button differently based on mode
        if (this.isEditMode()) {
            // In edit mode, make the cell relative and add button inside
            $cell.css('position', 'relative');
            $cell.append($toggleLink);
        } else {
            // In detail mode, use the existing logic
            $cell.append($toggleLink);
        }

        // Add click handler
        $toggleLink.on('click', (e) => {
            console.log('TextHideable: Button clicked!');
            e.preventDefault();
            e.stopPropagation();
            this.toggleVisibility();
        });

        // Add hover handlers only for detail mode (in edit mode button is always visible)
        if (this.isDetailMode()) {
            $cell
                .off('mouseenter.text-hideable mouseleave.text-hideable')
                .on('mouseenter.text-hideable', (e) => {
                    e.stopPropagation();
                    $toggleLink.removeClass('hidden');
                })
                .on('mouseleave.text-hideable', (e) => {
                    e.stopPropagation();
                    $toggleLink.addClass('hidden');
                });
        }

        console.log('TextHideable: Button initialized successfully');
    }
    };
});
