/************************************************************************
 * This file is part of EspoCRM.
 *
 * EspoCRM – Open Source CRM application.
 * Copyright (C) 2014-2024 <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON>
 * Website: https://www.espocrm.com
 *
 * This program is free software: you can redistribute it and/or modify
 * it under the terms of the GNU Affero General Public License as published by
 * the Free Software Foundation, either version 3 of the License, or
 * (at your option) any later version.
 *
 * This program is distributed in the hope that it will be useful,
 * but WITHOUT ANY WARRANTY; without even the implied warranty of
 * MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE. See the
 * GNU Affero General Public License for more details.
 *
 * You should have received a copy of the GNU Affero General Public License
 * along with this program. If not, see <https://www.gnu.org/licenses/>.
 *
 * The interactive user interfaces in modified source and object code versions
 * of this program must display Appropriate Legal Notices, as required under
 * Section 5 of the GNU Affero General Public License version 3.
 *
 * In accordance with Section 7(b) of the GNU Affero General Public License version 3,
 * these Appropriate Legal Notices must retain the display of the "EspoCRM" word.
 ************************************************************************/

define('text-hideable:views/fields/text-hideable', ['views/fields/varchar'], function (VarcharFieldView) {

    /**
     * A hideable varchar field that can toggle between visible and hidden (password-like) display.
     */
    return class extends VarcharFieldView {

        type = 'textHideable'

        detailTemplate = 'text-hideable:fields/text-hideable/detail'
        editTemplate = 'text-hideable:fields/text-hideable/edit'
        listTemplate = 'text-hideable:fields/text-hideable/list'
        searchTemplate = 'text-hideable:fields/text-hideable/search'

        events = {
            ...VarcharFieldView.prototype.events,
            /** @this TextHideableFieldView */
            'click .text-hideable-toggle-link': function () {
                this.toggleVisibility();
            },
            /** @this TextHideableFieldView */
            'click [data-action="toggleVisibility"]': function () {
                this.toggleVisibility();
            },
        }

    setup() {
        super.setup();

        this.isHidden = this.params.hideByDefault !== false;
        this.showToggleButton = this.params.showToggleButton !== false;
        this.dynamicButtonAdded = false;
    }

    /**
     * Toggle the visibility of the text content
     */
    toggleVisibility() {
        this.isHidden = !this.isHidden;

        if (this.mode === this.MODE_DETAIL) {
            this.updateToggleButtonIcon();
            this.updateContentDisplay();
        } else if (this.mode === this.MODE_EDIT) {
            this.updateInputDisplay();
            this.updateTemplateButtonIcon();
        } else if (this.mode === this.MODE_LIST || this.mode === this.MODE_LIST_LINK) {
            this.updateListViewContent();
        }
    }

    /**
     * Update the toggle button icon (only for detail mode dynamic button)
     */
    updateToggleButtonIcon() {
        if (this.mode !== this.MODE_DETAIL) {
            return;
        }

        const $cell = this.findFieldCell();
        const $icon = $cell.find('.text-hideable-toggle-link span');

        if ($icon.length) {
            $icon.removeClass('fa-eye fa-eye-slash')
                 .addClass(this.isHidden ? 'fa-eye' : 'fa-eye-slash');
        }
    }

    /**
     * Update the content display in detail mode without re-rendering
     */
    updateContentDisplay() {
        const $complexText = this.$el.find('.complex-text');
        if (!$complexText.length) return;

        if (this.isHidden) {
            const maskedValue = this.getMaskedValue();
            const $maskedSpan = $(`<span class="text-hideable-masked">${maskedValue}</span>`);
            $maskedSpan.css({
                'color': 'var(--text-gray-color)',
                'letter-spacing': '1px',
                'font-size': '0.9em'
            });
            $complexText.empty().append($maskedSpan);
        } else {
            const value = this.model.get(this.name);
            const content = value ?
                $('<div>').text(value).html() :
                `<span class="none-value">${this.translate('None')}</span>`;
            $complexText.html(content);
        }
    }

    /**
     * Update the template button icon in edit mode
     */
    updateTemplateButtonIcon() {
        const $icon = this.$el.find('.text-hideable-toggle-btn span');
        if ($icon.length) {
            $icon.removeClass('fa-eye fa-eye-slash')
                 .addClass(this.isHidden ? 'fa-eye' : 'fa-eye-slash');
        }
    }

    /**
     * Update the input display for edit mode
     */
    updateInputDisplay() {
        if (this.$element?.length) {
            this.$element.attr('type', this.isHidden ? 'password' : 'text');
        }
    }

    /**
     * Update the content display in list views
     */
    updateListViewContent() {
        if (this.isHidden) {
            const maskedValue = this.getMaskedValue();
            const $maskedSpan = $(`<span class="text-hideable-masked" style="color: var(--text-gray-color); letter-spacing: 1px; font-size: 0.9em;">${maskedValue}</span>`);
            this.$el.empty().append($maskedSpan);
        } else {
            const value = this.model.get(this.name);
            const content = value ?
                $('<div>').text(value).html() :
                `<span class="none-value">${this.translate('None')}</span>`;
            this.$el.html(content);
        }
    }

    data() {
        const data = super.data();

        data.isHidden = this.isHidden;
        data.showToggleButton = this.showToggleButton;
        data.toggleButtonIcon = this.isHidden ? 'fa-eye' : 'fa-eye-slash';

        if (this.isReadMode() && this.isHidden && data.isNotEmpty) {
            data.maskedValue = this.getMaskedValue();
        }

        return data;
    }

    /**
     * Get masked version of the value for display when hidden
     */
    getMaskedValue() {
        return this.model.get(this.name) ? '●●●●●●●' : '';
    }

    afterRender() {
        super.afterRender();

        if (this.mode === this.MODE_EDIT) {
            this.updateInputDisplay();
            this.removeDynamicButton();
            if (this.showToggleButton) {
                this.updateTemplateButtonIcon();
            }
        } else if (this.mode === this.MODE_DETAIL && this.showToggleButton) {
            setTimeout(() => {
                if (this.mode === this.MODE_DETAIL && !this.dynamicButtonAdded) {
                    this.dynamicButtonAdded = true;

                    if (this.isDetailSmallView()) {
                        this.initToggleButtonForSmallView();
                    } else {
                        this.initToggleButtonSimple();
                        this.monitorForEditButton();
                    }

                    this.checkInitialHoverState();
                }
            }, 10);
        } else if (this.isListMode() && this.showToggleButton) {
            this.initListViewToggle();
        }
    }

    /**
     * Monitor for the edit button and reorder when it appears (only for editable fields)
     */
    monitorForEditButton() {
        // Only monitor if inline edit is enabled
        const isInlineEditDisabled = this.inlineEditDisabled ||
                                   this.params.readOnly ||
                                   this.disabled ||
                                   !this.getAcl().checkField(this.entityType, this.name, 'edit');

        if (isInlineEditDisabled) {
            return; // No need to monitor for edit button
        }

        let attempts = 0;
        const maxAttempts = 20;

        const checkAndReorder = () => {
            attempts++;
            const $cell = this.findFieldCell();
            const $toggleLink = $cell.find('.text-hideable-toggle-link');
            const $editLink = $cell.find('.inline-edit-link');

            if ($toggleLink.length && $editLink.length) {
                $toggleLink.insertAfter($editLink);
                return;
            }

            if (attempts < maxAttempts) {
                setTimeout(checkAndReorder, 100);
            }
        };

        setTimeout(checkAndReorder, 50);
    }

    /**
     * Helper method to find the field cell container
     */
    findFieldCell() {
        let $cell = this.get$cell();

        if ($cell.length === 0) {
            $cell = this.$el.closest('.field-content');
        }
        if ($cell.length === 0) {
            $cell = this.$el.closest('.field').find('.field-content');
        }
        if ($cell.length === 0) {
            $cell = this.$el.closest('.field');
        }
        if ($cell.length === 0) {
            $cell = this.$el.parent();
        }

        return $cell;
    }

    /**
     * Check if we're in a detail-small view (popup)
     */
    isDetailSmallView() {
        return this.$el.closest('.modal, .detail-small, [data-name="detailSmall"]').length > 0;
    }

    /**
     * Check if mouse is already hovering and show button if needed
     */
    checkInitialHoverState() {
        const $cell = this.findFieldCell();
        const $toggleButton = $cell.find('.text-hideable-toggle-link');

        if ($toggleButton.length && $cell.is(':hover')) {
            setTimeout(() => $toggleButton.removeClass('hidden'), 50);
        }
    }

    /**
     * Create a toggle button with common properties
     */
    createToggleButton(extraCss = {}) {
        const baseCss = {
            'cursor': 'pointer',
            'color': '#888',
            'text-decoration': 'none'
        };

        const $toggleLink = $('<a>')
            .attr('role', 'button')
            .addClass('text-hideable-toggle-link hidden')
            .attr('title', this.translate('Toggle Visibility'))
            .css({ ...baseCss, ...extraCss })
            .append($('<span>').addClass(`fas ${this.isHidden ? 'fa-eye' : 'fa-eye-slash'} fa-sm`));

        // Add click and hover handlers
        $toggleLink
            .on('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.toggleVisibility();
            })
            .on('mouseenter', (e) => $(e.currentTarget).css('color', '#555'))
            .on('mouseleave', (e) => $(e.currentTarget).css('color', '#888'));

        return $toggleLink;
    }

    /**
     * Initialize click-to-toggle functionality for list views
     */
    initListViewToggle() {
        this.$el.css({
            'cursor': 'pointer',
            'user-select': 'none'
        });

        this.$el.on('click.text-hideable', (e) => {
            e.preventDefault();
            e.stopPropagation();
            this.toggleVisibility();
        });

        this.$el.on('mouseenter.text-hideable', () => this.$el.css('opacity', '0.8'));
        this.$el.on('mouseleave.text-hideable', () => this.$el.css('opacity', '1'));
    }

    /**
     * Remove dynamic toggle button and list view handlers
     */
    removeDynamicButton() {
        const $cell = this.findFieldCell();
        const $dynamicButton = $cell.find('.text-hideable-toggle-link');

        if ($dynamicButton.length) {
            $dynamicButton.remove();
            $cell.off('mouseenter.text-hideable mouseleave.text-hideable');
        }

        this.$el.off('click.text-hideable mouseenter.text-hideable mouseleave.text-hideable');
        this.$el.css({ 'cursor': '', 'user-select': '', 'opacity': '' });
        this.dynamicButtonAdded = false;
    }

    /**
     * Override to prevent flicker during inline edit transitions
     */
    inlineEditClose() {
        const wasHidden = this.isHidden;
        super.inlineEditClose();
        this.isHidden = wasHidden;
    }

    /**
     * Initialize toggle button for detail-small views (popups)
     */
    initToggleButtonForSmallView() {
        const $label = this.findLabelForSmallView();
        if (!$label) {
            return this.initToggleButtonSimple();
        }

        $label.find('.text-hideable-toggle-link').remove();

        const $toggleLink = this.createToggleButton({
            'margin-left': '8px',
            'display': 'inline'
        });

        $label.append($toggleLink);

        const $cell = $label.closest('tr, .row, .cell');
        $cell
            .off('mouseenter.text-hideable mouseleave.text-hideable')
            .on('mouseenter.text-hideable', (e) => {
                e.stopPropagation();
                $toggleLink.removeClass('hidden');
            })
            .on('mouseleave.text-hideable', (e) => {
                e.stopPropagation();
                $toggleLink.addClass('hidden');
            });
    }

    /**
     * Find label element for small views
     */
    findLabelForSmallView() {
        let $label = null;
        const $cell = this.$el.closest('tr, .row, .cell');

        if ($cell.length) {
            $label = $cell.find('.field-label, label').first();
        }

        if (!$label || $label.length === 0) {
            $label = $(`label[data-name="${this.name}"], .field-label[data-name="${this.name}"]`);
        }

        if (!$label || $label.length === 0) {
            $label = $('label, .field-label').filter((_, el) => {
                return $(el).text().trim() === this.getLabelText();
            });
        }

        return $label && $label.length ? $label : null;
    }

    /**
     * Initialize toggle button for detail mode
     */
    initToggleButtonSimple() {
        if (this.mode !== this.MODE_DETAIL) {
            return;
        }

        const $cell = this.findFieldCell();
        if ($cell.length === 0) {
            return;
        }

        $cell.find('.text-hideable-toggle-link').remove();

        // Check if inline edit is disabled by looking for readonly or inlineEditDisabled
        const isInlineEditDisabled = this.inlineEditDisabled ||
                                   this.params.readOnly ||
                                   this.disabled ||
                                   !this.getAcl().checkField(this.entityType, this.name, 'edit');

        const toggleCss = isInlineEditDisabled ? {
            'position': 'absolute',
            'right': '0px',
            'top': '0px'
        } : {
            'float': 'right',
            'margin-right': '8px'
        };

        const $toggleLink = this.createToggleButton(toggleCss);

        if (isInlineEditDisabled) {
            // For non-editable fields, position relative to the field content
            $cell.css('position', 'relative');
        }

        $cell.append($toggleLink);

        $cell
            .off('mouseenter.text-hideable mouseleave.text-hideable')
            .on('mouseenter.text-hideable', (e) => {
                e.stopPropagation();
                $toggleLink.removeClass('hidden');
            })
            .on('mouseleave.text-hideable', (e) => {
                e.stopPropagation();
                $toggleLink.addClass('hidden');
            });
    }
    };
});
