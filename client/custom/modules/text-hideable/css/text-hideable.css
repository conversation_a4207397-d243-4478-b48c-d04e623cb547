/* Text Hideable Field Styles */

/* Toggle button styling - similar to inline edit button */
a.text-hideable-toggle-link {
    margin-right: 5px;
    color: #888 !important;
    text-decoration: none !important;
}

a.text-hideable-toggle-link:hover {
    color: #555 !important;
    text-decoration: none !important;
}

/* Hidden text styling for edit mode */
.text-hideable-hidden {
    font-family: 'Courier New', monospace;
    -webkit-text-security: disc;
    text-security: disc;
    /* Fallback for browsers that don't support text-security */
    color: transparent;
    text-shadow: 0 0 0 #333;
}

/* For browsers that don't support text-security, use a different approach */
.text-hideable-hidden:not(:focus) {
    background: repeating-linear-gradient(
        90deg,
        transparent,
        transparent 1ch,
        #333 1ch,
        #333 1.2ch
    );
    color: transparent;
    background-size: 1.2ch 1em;
    background-position: 0 0;
    background-clip: text;
    -webkit-background-clip: text;
}

/* When focused, show the actual text for editing */
.text-hideable-hidden:focus {
    -webkit-text-security: none;
    text-security: none;
    color: inherit;
    text-shadow: none;
    background: none;
}

/* Masked text in list and detail views */
.text-hideable-masked {
    color: var(--text-gray-color) !important;
    letter-spacing: 1px;
    font-size: 0.9em;
}


