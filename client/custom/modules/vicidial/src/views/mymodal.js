define('vicidial:views/mymodal', ['views/modal', 'model'], function (Dep, Model) {

    return Dep.extend({

        className: 'dialog dialog-record',

        // template content can be defined right here or externally no-side-margin
        templateContent: `
            <div class="record">{{{record}}}</div>
        `,

        // template content can be defined in external file client/custom/res/templates/my-dialog.tpl 
        // template: 'custom:modals/my-dialog',

        // if true, clicking on the backdrop will close the dialog
        backdrop: 'static', // 'static', true, false

        setup: function () {
            Espo.ui.notify("Espere...", "warning");
            this.wait(true);

            Promise.all([
                Espo.Ajax.getRequest('Vicidial/repositories/campaigns?active=1').then(response => this.vd_campaigns = response.map(campaign => campaign.campaign_id).sort((a, b) => {
                    if (a < b) return -1;
                    if (b < a) return 1;
                    return 0;
                })),
                Espo.Ajax.getRequest('Vicidial/repositories/lists?active=1').then(response => this.vd_lists = response.reduce((acc, cur) => {
                    if (!acc[cur.campaign_id]) acc[cur.campaign_id] = [];
                    acc[cur.campaign_id].push(cur.list_id);
                    return acc;
                }, {}))
            ])
            .finally(() => {
                this.wait(false);
                Espo.ui.notify(false);
                
                let campView = this.getView('record').getField('targetVdCampaign');
                let listView = this.getView('record').getField('targetVdList');
                campView.on('change', () => {
                    listView.setOptionList(this.vd_lists[campView.getValueForDisplay()]);
                });
                campView.setOptionList(this.vd_campaigns);
            });
            // action buttons
            this.buttonList = [
                {
                    name: 'doSomething', // handler for 'doSomething' action is below
                    html: 'Enviar a lista',//this.translate('Some Action', 'labels', 'MyScope'), // button label 
                    style: 'primary',
                },
                {
                    name: 'cancel',
                    label: 'Cancelar',
                },
            ];

            let title = this.options.title || 'Enviar a VICIdial'; // assuming it's passed by our parent view

            this.headerText = title;             
            // this.headerHtml = this.getHelper().escapeString(title);

            this.formModel = new Model();
            this.formModel.name = 'None'; // dummy name, important

            // define fields
            this.formModel.setDefs({
                fields: {
                    'targetVdCampaign': {
                        type: 'varchar', // field type
                        view: 'views/fields/enum', // optional, to define custom view
                        required: true // field param
                    },
                    'options': {
                        type: 'checklist',
                        options: [
                            "test1",
                            "test2",
                            "test3"
                        ],
                        default: [
                            "test1",
                            "test3"
                        ]
                    },
                    'targetVdList': {
                        type: 'varchar',
                        view: 'views/fields/enum',
                        required: true
                    }
                }
            });

            this.createView('record', 'views/record/edit-for-modal', {
                scope: 'None', // dummy name
                model: this.formModel,
                el: this.getSelector() + ' .record',                
                detailLayout: [ // form layout
                    {
                        label: "Seleccione la lista de destino",
                        rows: [
                            [
                                {
                                    name: 'targetVdCampaign',
                                    labelText: "Campaña",
                                },
                                false,
                                false
                            ],
                            [
                                {
                                    name: 'targetVdList',
                                    labelText: "Lista"
                                },
                                false,
                                false
                            ],
                            [
                                {
                                    name: 'options',
                                    labelText: "Opciones",
                                },
                                false,
                                false
                            ]
                        ],
                    },
                ],
            });
        },

        afterRender: function () {
            this.listenTo(this.model, "change", (model, options) => {
                console.log("changed");
            });
        },

        actionDoSomething: function () {
            Espo.ui.notify("Espere...", "warning");
            const listId = this.getView('record').getField('targetVdList').getValueForDisplay();
            const vdOptions = this.getView('record').getField('options').selected;
            // console.log(this.formModel.get('someEnum'));
            // this.getView('record').getField('targetVdList').setOptionList(["1006","1007"]);

            // window.vdlist = this.getView('record').getField('targetVdList');
            // window.camp = this.getView('record').getField('targetVdCampaign');
            // window.rec = this.getView('record');
            // window.vd_lists = this.vd_lists;
            // window.loptions = this.options.id;
            // fetch data from form to model and validate
            // let isValid = this.getView('record').processFetch();

            // if (!isValid) { 
            //     return;
            // }

            // make POST request
            Espo.Ajax
                .postRequest('Vicidial/push-to-list', {
                    id: this.options.id, // passed from the parent view
                    options: vdOptions,
                    listId,
                    // someString: this.formModel.get('someString'),
                    // someCheckbox: this.formModel.get('someCheckbox'),
                })
                .then(response => {
                    Espo.Ui.success(this.translate('Done'));
                    // event 'done' will be caught by the parent view
                    console.log(response);
                    this.trigger('done', response);
                    this.close();
                });
        },
    });
});