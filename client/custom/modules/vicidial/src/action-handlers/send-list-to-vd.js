define('vicidial:action-handlers/send-list-to-vd', ['action-handler'], function (Dep) {
    return Dep.extend({
        templateContent: `
            <div>{{{mymodal}}}</div>
        `,
        actionSend: function (data, e) {
            this.view.createView('mymodal', 'vicidial:views/mymodal', { title: "Enviar a VICIdial", id: this.view.model.id }, view => {
                view.render();

                this.view.listenToOnce(view, 'done', response => {
                    console.log(response);
                });
            });
            // Espo.Ajax
            // .getRequest('Lead/' + this.view.model.id)
            // .then(response => {
            //     console.log(response);
            // });
            // Espo.Ui.confirm("¿Desea enviar estos registros a VICIdial?", {
            //     confirmText: "Confirmar",
            //     cancelText: "Cancelar"
            // }).then(() => {
            //     // here do some actions
                // console.dir(this);
                // console.log(Espo);
                // console.log(data);
            //     console.log(this.translate);
            // });
        }
    });
});
    