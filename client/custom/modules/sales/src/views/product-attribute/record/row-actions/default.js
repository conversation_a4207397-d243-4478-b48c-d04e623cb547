/***********************************************************************************
 * The contents of this file are subject to the Extension License Agreement
 * ("Agreement") which can be viewed at
 * https://www.espocrm.com/extension-license-agreement/.
 * By copying, installing downloading, or using this file, You have unconditionally
 * agreed to the terms and conditions of the Agreement, and You may not use this
 * file except in compliance with the Agreement. Under the terms of the Agreement,
 * You shall not license, sublicense, sell, resell, rent, lease, lend, distribute,
 * redistribute, market, publish, commercialize, or otherwise transfer rights or
 * usage to the software or any modified version or derivative work of the software
 * created by or for you.
 *
 * Copyright (C) 2015-2024 Letrium Ltd.
 *
 * License ID: 
 ************************************************************************************/

define('sales:views/product-attribute/record/row-actions/default',
['views/record/row-actions/default'], function (Dep) {

    return class extends Dep {

        getActionList() {
            const list = super.getActionList();

            if (!this.getAcl().checkScope('ProductAttribute', 'edit')) {
                return list;
            }

            return list.concat([
                {
                    action: 'move',
                    label: 'Move To Top',
                    data: {
                        id: this.model.id,
                        type: 'top',
                    },
                },
                {
                    action: 'move',
                    label: 'Move Up',
                    data: {
                        id: this.model.id,
                        type: 'up',
                    },
                },
                {
                    action: 'move',
                    label: 'Move Down',
                    data: {
                        id: this.model.id,
                        type: 'down',
                    },
                },
                {
                    action: 'move',
                    label: 'Move To Bottom',
                    data: {
                        id: this.model.id,
                        type: 'bottom',
                    },
                },
            ]);
        }
    };
});
