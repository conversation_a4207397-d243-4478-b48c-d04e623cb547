/* Text Hideable Field Styles */

.text-hideable-container {
    position: relative;
}

.text-hideable-controls {
    margin-bottom: 5px;
}

.text-hideable-controls .btn {
    font-size: 12px;
    padding: 2px 8px;
}

.text-hideable-controls .toggle-text {
    margin-left: 4px;
}

/* Hidden text styling for edit mode */
.text-hideable-hidden {
    font-family: 'Courier New', monospace;
    -webkit-text-security: disc;
    text-security: disc;
    /* Fallback for browsers that don't support text-security */
    color: transparent;
    text-shadow: 0 0 0 #333;
}

/* For browsers that don't support text-security, use a different approach */
.text-hideable-hidden:not(:focus) {
    background: repeating-linear-gradient(
        90deg,
        transparent,
        transparent 1ch,
        #333 1ch,
        #333 1.2ch
    );
    color: transparent;
    background-size: 1.2ch 1em;
    background-position: 0 0;
    background-clip: text;
    -webkit-background-clip: text;
}

/* When focused, show the actual text for editing */
.text-hideable-hidden:focus {
    -webkit-text-security: none;
    text-security: none;
    color: inherit;
    text-shadow: none;
    background: none;
}

/* Masked text in list and detail views */
.text-hideable-masked {
    font-family: 'Courier New', monospace;
    color: #666;
    font-style: italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .text-hideable-controls .toggle-text {
        display: none;
    }
    
    .text-hideable-controls .btn {
        padding: 4px 6px;
    }
}
