{{#if isNotEmpty}}
<div class="text-hideable-container">
    {{#if showToggleButton}}
    <div class="text-hideable-controls">
        <button
            type="button"
            class="btn btn-sm btn-default"
            data-action="toggleVisibility"
            title="{{translate 'Toggle Visibility'}}"
        >
            <span class="fas {{toggleButtonIcon}}"></span>
            <span class="toggle-text">{{toggleButtonText}}</span>
        </button>
    </div>
    {{/if}}
    
    <div
        class="complex-text-container{{#if isCut}} cut{{/if}}{{#if isHidden}} text-hideable-hidden{{/if}}"
        {{#if cutHeight}} style="max-height: {{cutHeight}}px;"{{/if}}
    >
        <div class="complex-text">
            {{#if isHidden}}
                {{#unless displayRawText}}{{complexText maskedValue}}{{else}}{{breaklines maskedValue}}{{/unless}}
            {{else}}
                {{#unless displayRawText}}{{#if htmlValue}}{{{htmlValue}}}{{else}}{{complexText value}}{{/if}}{{else}}{{breaklines value}}{{/unless}}
            {{/if}}
        </div>
    </div>
    
    {{#if isCut}}
    <div class="see-more-container hidden">
        <a
            role="button"
            tabindex="0"
            data-action="seeMoreText"
        ><span class="fas fa-sm fa-chevron-down"></span> <span class="text">{{translate 'See more'}}</span></a>
    </div>
    {{/if}}
</div>
{{else}}
    {{#if valueIsSet}}<span class="none-value">{{translate 'None'}}</span>{{else}}
    <span class="loading-value"></span>{{/if}}
{{/if}}
